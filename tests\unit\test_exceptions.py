#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常模块单元测试

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import unittest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.exceptions import (
    DataStudioException, ConfigurationError, ConfigFileNotFoundError,
    ConfigValidationError, CommunicationError, SerialConnectionError,
    SerialTimeoutError, DataProcessingError, FrameDetectionError,
    DataParsingError, ValidationError, BusinessLogicError,
    ProtocolFlowError, CommandExecutionError, SystemError,
    InitializationError, format_exception_info, is_recoverable_error,
    get_error_severity
)


class TestDataStudioException(unittest.TestCase):
    """基础异常类测试"""
    
    def test_basic_exception(self):
        """测试基础异常功能"""
        exc = DataStudioException("Test message")
        self.assertEqual(str(exc), "Test message")
        self.assertEqual(exc.message, "Test message")
        self.assertIsNone(exc.error_code)
        self.assertEqual(exc.details, {})
    
    def test_exception_with_code(self):
        """测试带错误代码的异常"""
        exc = DataStudioException("Test message", error_code="TEST_ERROR")
        self.assertEqual(str(exc), "[TEST_ERROR] Test message")
        self.assertEqual(exc.error_code, "TEST_ERROR")
    
    def test_exception_with_details(self):
        """测试带详细信息的异常"""
        details = {"field": "value", "count": 123}
        exc = DataStudioException("Test message", details=details)
        self.assertEqual(exc.details, details)
    
    def test_to_dict(self):
        """测试异常转字典"""
        exc = DataStudioException(
            "Test message", 
            error_code="TEST_ERROR",
            details={"key": "value"}
        )
        
        result = exc.to_dict()
        expected = {
            "type": "DataStudioException",
            "message": "Test message",
            "error_code": "TEST_ERROR",
            "details": {"key": "value"}
        }
        self.assertEqual(result, expected)


class TestConfigurationExceptions(unittest.TestCase):
    """配置相关异常测试"""
    
    def test_config_file_not_found_error(self):
        """测试配置文件未找到异常"""
        exc = ConfigFileNotFoundError("/path/to/config.json")
        self.assertIn("/path/to/config.json", str(exc))
        self.assertEqual(exc.error_code, "CONFIG_FILE_NOT_FOUND")
        self.assertEqual(exc.details["file_path"], "/path/to/config.json")
    
    def test_config_validation_error(self):
        """测试配置验证异常"""
        validation_errors = ["Field required", "Invalid type"]
        exc = ConfigValidationError(
            "Validation failed",
            field_path="serial.baudrate",
            validation_errors=validation_errors
        )
        
        self.assertEqual(exc.error_code, "CONFIG_VALIDATION_ERROR")
        self.assertEqual(exc.details["field_path"], "serial.baudrate")
        self.assertEqual(exc.details["validation_errors"], validation_errors)


class TestCommunicationExceptions(unittest.TestCase):
    """通信相关异常测试"""
    
    def test_serial_connection_error(self):
        """测试串口连接异常"""
        exc = SerialConnectionError("COM1")
        self.assertIn("COM1", str(exc))
        self.assertEqual(exc.error_code, "SERIAL_CONNECTION_ERROR")
        self.assertEqual(exc.details["port"], "COM1")
    
    def test_serial_timeout_error(self):
        """测试串口超时异常"""
        exc = SerialTimeoutError("read", 5.0)
        self.assertIn("read", str(exc))
        self.assertIn("5.0", str(exc))
        self.assertEqual(exc.error_code, "SERIAL_TIMEOUT_ERROR")
        self.assertEqual(exc.details["operation"], "read")
        self.assertEqual(exc.details["timeout"], 5.0)


class TestDataProcessingExceptions(unittest.TestCase):
    """数据处理相关异常测试"""
    
    def test_frame_detection_error(self):
        """测试帧检测异常"""
        raw_data = b'\x01\x02\x03'
        exc = FrameDetectionError("Invalid frame header", raw_data)
        
        self.assertIn("帧检测失败", str(exc))
        self.assertEqual(exc.error_code, "FRAME_DETECTION_ERROR")
        self.assertEqual(exc.details["raw_data"], raw_data.hex())
    
    def test_data_parsing_error(self):
        """测试数据解析异常"""
        raw_data = b'\xFF\xFF'
        exc = DataParsingError(
            "Invalid data format",
            field_name="temperature",
            raw_data=raw_data
        )
        
        self.assertIn("数据解析失败", str(exc))
        self.assertEqual(exc.error_code, "DATA_PARSING_ERROR")
        self.assertEqual(exc.details["field_name"], "temperature")
        self.assertEqual(exc.details["raw_data"], raw_data.hex())
    
    def test_validation_error(self):
        """测试验证异常"""
        exc = ValidationError(
            "Checksum mismatch",
            expected="0xAB",
            actual="0xCD"
        )
        
        self.assertIn("数据验证失败", str(exc))
        self.assertEqual(exc.error_code, "VALIDATION_ERROR")
        self.assertEqual(exc.details["expected"], "0xAB")
        self.assertEqual(exc.details["actual"], "0xCD")


class TestBusinessLogicExceptions(unittest.TestCase):
    """业务逻辑相关异常测试"""
    
    def test_protocol_flow_error(self):
        """测试协议流程异常"""
        exc = ProtocolFlowError("Invalid state transition", current_step="init")
        
        self.assertIn("协议流程错误", str(exc))
        self.assertEqual(exc.error_code, "PROTOCOL_FLOW_ERROR")
        self.assertEqual(exc.details["current_step"], "init")
    
    def test_command_execution_error(self):
        """测试指令执行异常"""
        exc = CommandExecutionError("READ_DATA")
        
        self.assertIn("READ_DATA", str(exc))
        self.assertEqual(exc.error_code, "COMMAND_EXECUTION_ERROR")
        self.assertEqual(exc.details["command"], "READ_DATA")


class TestSystemExceptions(unittest.TestCase):
    """系统相关异常测试"""
    
    def test_initialization_error(self):
        """测试初始化异常"""
        exc = InitializationError("SerialManager")
        
        self.assertIn("SerialManager", str(exc))
        self.assertEqual(exc.error_code, "INITIALIZATION_ERROR")
        self.assertEqual(exc.details["component"], "SerialManager")


class TestExceptionUtilities(unittest.TestCase):
    """异常工具函数测试"""
    
    def test_format_exception_info(self):
        """测试异常信息格式化"""
        # 测试自定义异常
        exc = DataStudioException("Test", error_code="TEST")
        result = format_exception_info(exc)
        
        self.assertEqual(result["type"], "DataStudioException")
        self.assertEqual(result["message"], "Test")
        self.assertEqual(result["error_code"], "TEST")
        
        # 测试标准异常
        std_exc = ValueError("Standard error")
        result = format_exception_info(std_exc)
        
        self.assertEqual(result["type"], "ValueError")
        self.assertEqual(result["message"], "Standard error")
        self.assertIsNone(result["error_code"])
    
    def test_is_recoverable_error(self):
        """测试异常可恢复性判断"""
        # 可恢复异常
        recoverable_exceptions = [
            SerialTimeoutError("read", 1.0),
            FrameDetectionError("Invalid header"),
            ValidationError("Checksum error")
        ]
        
        for exc in recoverable_exceptions:
            self.assertTrue(is_recoverable_error(exc))
        
        # 不可恢复异常
        non_recoverable_exceptions = [
            ConfigFileNotFoundError("/path/config.json"),
            InitializationError("Component")
        ]
        
        for exc in non_recoverable_exceptions:
            self.assertFalse(is_recoverable_error(exc))
    
    def test_get_error_severity(self):
        """测试异常严重程度获取"""
        # 低严重程度
        low_severity_exceptions = [
            SerialTimeoutError("read", 1.0),
            FrameDetectionError("Invalid header"),
            ValidationError("Checksum error")
        ]
        
        for exc in low_severity_exceptions:
            self.assertEqual(get_error_severity(exc), "low")
        
        # 中等严重程度
        medium_severity_exceptions = [
            DataParsingError("Parse failed"),
            CommandExecutionError("CMD_FAILED")
        ]
        
        for exc in medium_severity_exceptions:
            self.assertEqual(get_error_severity(exc), "medium")
        
        # 高严重程度
        high_severity_exceptions = [
            SerialConnectionError("COM1"),
            ConfigValidationError("Invalid config"),
            ProtocolFlowError("Flow error")
        ]
        
        for exc in high_severity_exceptions:
            self.assertEqual(get_error_severity(exc), "high")
        
        # 严重程度
        critical_severity_exceptions = [
            ConfigFileNotFoundError("/path/config.json"),
            InitializationError("Component")
        ]
        
        for exc in critical_severity_exceptions:
            self.assertEqual(get_error_severity(exc), "critical")
        
        # 未知异常默认中等严重程度
        unknown_exc = ValueError("Unknown error")
        self.assertEqual(get_error_severity(unknown_exc), "medium")


if __name__ == '__main__':
    unittest.main()
