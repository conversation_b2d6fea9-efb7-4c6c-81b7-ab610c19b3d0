{"system": {"name": "DataStudio", "version": "1.0.0", "description": "基于五层架构的动态配置数据采集系统"}, "buffer": {"size": 4096, "warning_threshold": 0.8, "critical_threshold": 0.95}, "queue": {"size": 100, "warning_threshold": 0.8, "critical_threshold": 0.95, "batch_size": 10, "timeout": 1.0}, "error_handling": {"max_consecutive_errors": 5, "retry_delay": 1.0, "max_retry_attempts": 3, "recovery_timeout": 10.0}, "logging": {"level": "INFO", "max_file_size": 10485760, "max_files": 5, "console_output": true, "file_output": true}, "performance": {"target_processing_rate": 1000, "max_response_time": 0.1, "memory_warning_threshold": 104857600, "cpu_warning_threshold": 0.8}}