#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统常量定义
定义数据采集系统中使用的所有常量

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

from enum import Enum, IntEnum
from typing import Dict, Any

# ============================================================================
# 系统版本信息
# ============================================================================
SYSTEM_VERSION = "1.0.0"
SYSTEM_NAME = "DataStudio"
SYSTEM_DESCRIPTION = "基于五层架构的动态配置数据采集系统"

# ============================================================================
# 文件路径常量
# ============================================================================
DEFAULT_CONFIG_DIR = "config"
DEFAULT_PROTOCOLS_DIR = "config/protocols"
DEFAULT_LOGS_DIR = "logs"
DEFAULT_TESTS_DIR = "tests"

# 配置文件扩展名
CONFIG_FILE_EXTENSION = ".json"
LOG_FILE_EXTENSION = ".log"

# ============================================================================
# 串口通信常量
# ============================================================================
class SerialDefaults:
    """串口默认配置"""
    BAUDRATE = 9600
    BYTESIZE = 8
    PARITY = 'N'  # None
    STOPBITS = 1
    TIMEOUT = 1.0
    WRITE_TIMEOUT = 1.0
    
    # 支持的波特率
    SUPPORTED_BAUDRATES = [
        1200, 2400, 4800, 9600, 14400, 19200, 38400, 
        57600, 115200, 230400, 460800, 921600
    ]
    
    # 支持的数据位
    SUPPORTED_BYTESIZES = [5, 6, 7, 8]
    
    # 支持的校验位
    SUPPORTED_PARITIES = ['N', 'E', 'O', 'M', 'S']  # None, Even, Odd, Mark, Space
    
    # 支持的停止位
    SUPPORTED_STOPBITS = [1, 1.5, 2]

# ============================================================================
# 缓冲区常量
# ============================================================================
class BufferDefaults:
    """缓冲区默认配置"""
    BUFFER_SIZE = 4096  # 4KB循环缓冲区
    MAX_FRAME_SIZE = 1024  # 最大帧大小
    MIN_FRAME_SIZE = 4  # 最小帧大小
    BUFFER_WARNING_THRESHOLD = 0.8  # 缓冲区警告阈值 (80%)
    BUFFER_CRITICAL_THRESHOLD = 0.95  # 缓冲区严重阈值 (95%)

# ============================================================================
# 队列管理常量
# ============================================================================
class QueueDefaults:
    """队列默认配置"""
    QUEUE_SIZE = 100  # 默认队列大小
    QUEUE_WARNING_THRESHOLD = 0.8  # 队列警告阈值 (80%)
    QUEUE_CRITICAL_THRESHOLD = 0.95  # 队列严重阈值 (95%)
    BATCH_PROCESS_SIZE = 10  # 批处理大小
    QUEUE_TIMEOUT = 1.0  # 队列操作超时时间

# ============================================================================
# 数据类型常量
# ============================================================================
class DataTypes(Enum):
    """支持的数据类型"""
    INT8 = "int8"
    UINT8 = "uint8"
    INT16 = "int16"
    UINT16 = "uint16"
    INT32 = "int32"
    UINT32 = "uint32"
    FLOAT32 = "float32"
    FLOAT64 = "float64"
    STRING = "string"
    BYTES = "bytes"

# 数据类型大小映射 (字节)
DATA_TYPE_SIZES: Dict[str, int] = {
    DataTypes.INT8.value: 1,
    DataTypes.UINT8.value: 1,
    DataTypes.INT16.value: 2,
    DataTypes.UINT16.value: 2,
    DataTypes.INT32.value: 4,
    DataTypes.UINT32.value: 4,
    DataTypes.FLOAT32.value: 4,
    DataTypes.FLOAT64.value: 8,
}

# struct格式字符映射
STRUCT_FORMAT_MAP: Dict[str, str] = {
    DataTypes.INT8.value: 'b',
    DataTypes.UINT8.value: 'B',
    DataTypes.INT16.value: 'h',
    DataTypes.UINT16.value: 'H',
    DataTypes.INT32.value: 'i',
    DataTypes.UINT32.value: 'I',
    DataTypes.FLOAT32.value: 'f',
    DataTypes.FLOAT64.value: 'd',
}

# ============================================================================
# 字节序常量
# ============================================================================
class ByteOrder(Enum):
    """字节序定义"""
    LITTLE_ENDIAN = "little"  # 小端
    BIG_ENDIAN = "big"  # 大端

# struct字节序前缀
BYTE_ORDER_PREFIX: Dict[str, str] = {
    ByteOrder.LITTLE_ENDIAN.value: '<',
    ByteOrder.BIG_ENDIAN.value: '>',
}

# ============================================================================
# 协议工作模式常量
# ============================================================================
class WorkMode(Enum):
    """协议工作模式"""
    SINGLE_COMMAND = "single_command"  # 单次指令模式
    CONTINUOUS_COMMAND = "continuous_command"  # 循环查询模式
    MIXED_MODE = "mixed_mode"  # 混合模式

# ============================================================================
# 验证策略常量
# ============================================================================
class ValidationStrategy(Enum):
    """应答验证策略"""
    EXACT_MATCH = "exact_match"  # 精确匹配
    REGEX_MATCH = "regex_match"  # 正则表达式匹配
    LENGTH_CHECK = "length_check"  # 长度检查
    CHECKSUM_VERIFY = "checksum_verify"  # 校验和验证

# ============================================================================
# 错误处理常量
# ============================================================================
class ErrorDefaults:
    """错误处理默认配置"""
    MAX_CONSECUTIVE_ERRORS = 5  # 最大连续错误次数
    ERROR_RETRY_DELAY = 1.0  # 错误重试延迟 (秒)
    MAX_RETRY_ATTEMPTS = 3  # 最大重试次数
    ERROR_RECOVERY_TIMEOUT = 10.0  # 错误恢复超时 (秒)

class ErrorType(IntEnum):
    """错误类型"""
    COMMUNICATION_ERROR = 1  # 通信错误
    FRAME_DETECTION_ERROR = 2  # 帧检测错误
    DATA_PARSING_ERROR = 3  # 数据解析错误
    VALIDATION_ERROR = 4  # 验证错误
    CONFIGURATION_ERROR = 5  # 配置错误
    SYSTEM_ERROR = 6  # 系统错误

# ============================================================================
# 日志配置常量
# ============================================================================
class LogDefaults:
    """日志默认配置"""
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_LOG_FILES = 5  # 保留最多5个日志文件
    LOG_ENCODING = "utf-8"

# 日志级别映射
LOG_LEVELS: Dict[str, int] = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50,
}

# ============================================================================
# 性能监控常量
# ============================================================================
class PerformanceDefaults:
    """性能监控默认配置"""
    TARGET_PROCESSING_RATE = 1000  # 目标处理速度 (帧/秒)
    MAX_RESPONSE_TIME = 0.1  # 最大响应时间 (秒)
    MEMORY_WARNING_THRESHOLD = 100 * 1024 * 1024  # 内存警告阈值 (100MB)
    CPU_WARNING_THRESHOLD = 0.8  # CPU使用率警告阈值 (80%)

# ============================================================================
# 配置验证常量
# ============================================================================
class ConfigDefaults:
    """配置验证默认值"""
    MIN_PORT_NUMBER = 1
    MAX_PORT_NUMBER = 65535
    MIN_TIMEOUT = 0.1
    MAX_TIMEOUT = 60.0
    MIN_BUFFER_SIZE = 256
    MAX_BUFFER_SIZE = 65536

# ============================================================================
# 系统状态常量
# ============================================================================
class SystemState(Enum):
    """系统运行状态"""
    INITIALIZING = "initializing"  # 初始化中
    READY = "ready"  # 就绪
    RUNNING = "running"  # 运行中
    PAUSED = "paused"  # 暂停
    STOPPING = "stopping"  # 停止中
    STOPPED = "stopped"  # 已停止
    ERROR = "error"  # 错误状态

# ============================================================================
# 默认配置模板
# ============================================================================
DEFAULT_SYSTEM_CONFIG: Dict[str, Any] = {
    "system": {
        "name": SYSTEM_NAME,
        "version": SYSTEM_VERSION,
        "description": SYSTEM_DESCRIPTION
    },
    "serial": {
        "baudrate": SerialDefaults.BAUDRATE,
        "bytesize": SerialDefaults.BYTESIZE,
        "parity": SerialDefaults.PARITY,
        "stopbits": SerialDefaults.STOPBITS,
        "timeout": SerialDefaults.TIMEOUT
    },
    "buffer": {
        "size": BufferDefaults.BUFFER_SIZE,
        "warning_threshold": BufferDefaults.BUFFER_WARNING_THRESHOLD
    },
    "queue": {
        "size": QueueDefaults.QUEUE_SIZE,
        "warning_threshold": QueueDefaults.QUEUE_WARNING_THRESHOLD,
        "batch_size": QueueDefaults.BATCH_PROCESS_SIZE
    },
    "error_handling": {
        "max_consecutive_errors": ErrorDefaults.MAX_CONSECUTIVE_ERRORS,
        "retry_delay": ErrorDefaults.ERROR_RETRY_DELAY,
        "max_retry_attempts": ErrorDefaults.MAX_RETRY_ATTEMPTS
    },
    "logging": {
        "level": LogDefaults.LOG_LEVEL,
        "max_file_size": LogDefaults.MAX_LOG_FILE_SIZE,
        "max_files": LogDefaults.MAX_LOG_FILES
    }
}
