#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助工具函数单元测试

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import unittest
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.helper_utils import (
    HexUtils, DataConverter, TimeUtils, FileUtils, ValidationUtils, StringUtils,
    bytes_to_hex, hex_to_bytes, get_timestamp, get_formatted_time
)


class TestHexUtils(unittest.TestCase):
    """十六进制工具测试类"""
    
    def test_bytes_to_hex_string(self):
        """测试字节转十六进制字符串"""
        # 测试正常情况
        data = b'\x01\x02\x03\xFF'
        result = HexUtils.bytes_to_hex_string(data)
        self.assertEqual(result, "01 02 03 FF")
        
        # 测试自定义分隔符
        result = HexUtils.bytes_to_hex_string(data, separator="-")
        self.assertEqual(result, "01-02-03-FF")
        
        # 测试空数据
        result = HexUtils.bytes_to_hex_string(b'')
        self.assertEqual(result, "")
        
        # 测试错误输入
        with self.assertRaises(TypeError):
            HexUtils.bytes_to_hex_string("not bytes")
    
    def test_hex_string_to_bytes(self):
        """测试十六进制字符串转字节"""
        # 测试正常情况
        hex_str = "01 02 03 FF"
        result = HexUtils.hex_string_to_bytes(hex_str)
        self.assertEqual(result, b'\x01\x02\x03\xFF')
        
        # 测试无分隔符
        hex_str = "010203FF"
        result = HexUtils.hex_string_to_bytes(hex_str)
        self.assertEqual(result, b'\x01\x02\x03\xFF')
        
        # 测试小写
        hex_str = "01 02 03 ff"
        result = HexUtils.hex_string_to_bytes(hex_str)
        self.assertEqual(result, b'\x01\x02\x03\xFF')
        
        # 测试错误输入
        with self.assertRaises(ValueError):
            HexUtils.hex_string_to_bytes("")  # 空字符串

        with self.assertRaises(ValueError):
            HexUtils.hex_string_to_bytes("   ")  # 只有空格

        with self.assertRaises(ValueError):
            HexUtils.hex_string_to_bytes("GGGG")  # 全部无效字符
    
    def test_is_valid_hex_string(self):
        """测试十六进制字符串验证"""
        self.assertTrue(HexUtils.is_valid_hex_string("01 02 03 FF"))
        self.assertTrue(HexUtils.is_valid_hex_string("010203FF"))
        self.assertTrue(HexUtils.is_valid_hex_string("01-02-03-ff"))
        
        self.assertFalse(HexUtils.is_valid_hex_string(""))  # 空字符串
        self.assertFalse(HexUtils.is_valid_hex_string("   "))  # 只有空格
        self.assertFalse(HexUtils.is_valid_hex_string("GGGG"))  # 全部无效字符
        self.assertFalse(HexUtils.is_valid_hex_string(123))  # 非字符串
    
    def test_calculate_checksum(self):
        """测试校验和计算"""
        data = b'\x01\x02\x03\x04'
        
        # 测试XOR校验和
        xor_checksum = HexUtils.calculate_checksum(data, "xor")
        self.assertEqual(xor_checksum, 0x01 ^ 0x02 ^ 0x03 ^ 0x04)
        
        # 测试SUM校验和
        sum_checksum = HexUtils.calculate_checksum(data, "sum")
        self.assertEqual(sum_checksum, (0x01 + 0x02 + 0x03 + 0x04) & 0xFF)
        
        # 测试CRC8校验和
        crc8_checksum = HexUtils.calculate_checksum(data, "crc8")
        self.assertIsInstance(crc8_checksum, int)
        self.assertTrue(0 <= crc8_checksum <= 255)
        
        # 测试不支持的类型
        with self.assertRaises(ValueError):
            HexUtils.calculate_checksum(data, "unsupported")


class TestDataConverter(unittest.TestCase):
    """数据转换工具测试类"""
    
    def test_parse_value_by_type(self):
        """测试按类型解析数据"""
        # 测试int16解析
        data = b'\x01\x02'  # 小端：0x0201 = 513
        result = DataConverter.parse_value_by_type(data, "int16", "little")
        self.assertEqual(result, 513)
        
        # 测试大端
        result = DataConverter.parse_value_by_type(data, "int16", "big")
        self.assertEqual(result, 258)  # 0x0102 = 258
        
        # 测试float32
        import struct
        float_data = struct.pack('<f', 3.14)
        result = DataConverter.parse_value_by_type(float_data, "float32", "little")
        self.assertAlmostEqual(result, 3.14, places=5)
        
        # 测试字符串
        string_data = b'hello'
        result = DataConverter.parse_value_by_type(string_data, "string")
        self.assertEqual(result, "hello")
        
        # 测试字节
        result = DataConverter.parse_value_by_type(data, "bytes")
        self.assertEqual(result, data)
    
    def test_pack_value_by_type(self):
        """测试按类型打包数据"""
        # 测试int16打包
        result = DataConverter.pack_value_by_type(513, "int16", "little")
        self.assertEqual(result, b'\x01\x02')
        
        # 测试字符串打包
        result = DataConverter.pack_value_by_type("hello", "string")
        self.assertEqual(result, b'hello')
    
    def test_apply_scaling(self):
        """测试缩放应用"""
        # 测试基本缩放
        result = DataConverter.apply_scaling(100, scale=0.1, offset=5.0)
        self.assertEqual(result, 15.0)  # (100 * 0.1) + 5.0
        
        # 测试无缩放
        result = DataConverter.apply_scaling(100)
        self.assertEqual(result, 100.0)


class TestTimeUtils(unittest.TestCase):
    """时间工具测试类"""
    
    def test_get_timestamp(self):
        """测试获取时间戳"""
        timestamp = TimeUtils.get_timestamp()
        self.assertIsInstance(timestamp, float)
        self.assertGreater(timestamp, 0)
    
    def test_get_formatted_time(self):
        """测试格式化时间"""
        # 测试默认格式
        formatted = TimeUtils.get_formatted_time(1609459200)  # 2021-01-01 00:00:00 UTC
        self.assertIsInstance(formatted, str)
        self.assertIn("2021", formatted)
        
        # 测试自定义格式
        formatted = TimeUtils.get_formatted_time(1609459200, "%Y-%m-%d")
        self.assertIn("2021-01-01", formatted)
    
    def test_get_milliseconds(self):
        """测试获取毫秒时间戳"""
        ms_timestamp = TimeUtils.get_milliseconds()
        self.assertIsInstance(ms_timestamp, int)
        self.assertGreater(ms_timestamp, 0)


class TestValidationUtils(unittest.TestCase):
    """验证工具测试类"""
    
    def test_is_valid_port_number(self):
        """测试端口号验证"""
        self.assertTrue(ValidationUtils.is_valid_port_number(80))
        self.assertTrue(ValidationUtils.is_valid_port_number("8080"))
        self.assertTrue(ValidationUtils.is_valid_port_number(65535))
        
        self.assertFalse(ValidationUtils.is_valid_port_number(0))
        self.assertFalse(ValidationUtils.is_valid_port_number(65536))
        self.assertFalse(ValidationUtils.is_valid_port_number("invalid"))
    
    def test_is_valid_baudrate(self):
        """测试波特率验证"""
        self.assertTrue(ValidationUtils.is_valid_baudrate(9600))
        self.assertTrue(ValidationUtils.is_valid_baudrate("115200"))
        
        self.assertFalse(ValidationUtils.is_valid_baudrate(1234))  # 不在支持列表中
        self.assertFalse(ValidationUtils.is_valid_baudrate("invalid"))
    
    def test_is_valid_timeout(self):
        """测试超时时间验证"""
        self.assertTrue(ValidationUtils.is_valid_timeout(1.0))
        self.assertTrue(ValidationUtils.is_valid_timeout("5.5"))
        self.assertTrue(ValidationUtils.is_valid_timeout(30))
        
        self.assertFalse(ValidationUtils.is_valid_timeout(0.05))  # 太小
        self.assertFalse(ValidationUtils.is_valid_timeout(100))   # 太大
        self.assertFalse(ValidationUtils.is_valid_timeout("invalid"))


class TestStringUtils(unittest.TestCase):
    """字符串工具测试类"""
    
    def test_truncate_string(self):
        """测试字符串截断"""
        text = "This is a long string"
        result = StringUtils.truncate_string(text, 10)
        self.assertEqual(result, "This is...")
        
        # 测试短字符串
        short_text = "Short"
        result = StringUtils.truncate_string(short_text, 10)
        self.assertEqual(result, "Short")
    
    def test_format_bytes_size(self):
        """测试字节大小格式化"""
        self.assertEqual(StringUtils.format_bytes_size(0), "0 B")
        self.assertEqual(StringUtils.format_bytes_size(1024), "1.0 KB")
        self.assertEqual(StringUtils.format_bytes_size(1024 * 1024), "1.0 MB")
        self.assertEqual(StringUtils.format_bytes_size(1536), "1.5 KB")


class TestConvenienceFunctions(unittest.TestCase):
    """便捷函数测试类"""
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        # 测试bytes_to_hex
        data = b'\x01\x02'
        result = bytes_to_hex(data)
        self.assertEqual(result, "01 02")
        
        # 测试hex_to_bytes
        result = hex_to_bytes("01 02")
        self.assertEqual(result, b'\x01\x02')
        
        # 测试get_timestamp
        timestamp = get_timestamp()
        self.assertIsInstance(timestamp, float)
        
        # 测试get_formatted_time
        formatted = get_formatted_time()
        self.assertIsInstance(formatted, str)


if __name__ == '__main__':
    unittest.main()
