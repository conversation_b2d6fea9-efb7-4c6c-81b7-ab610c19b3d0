#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常量模块单元测试

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import unittest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.constants import (
    SYSTEM_VERSION, SYSTEM_NAME, SerialDefaults, BufferDefaults,
    QueueDefaults, DataTypes, DATA_TYPE_SIZES, STRUCT_FORMAT_MAP,
    ByteOrder, BYTE_ORDER_PREFIX, WorkMode, ValidationStrategy,
    ErrorDefaults, ErrorType, LogDefaults, DEFAULT_SYSTEM_CONFIG
)


class TestConstants(unittest.TestCase):
    """常量模块测试类"""
    
    def test_system_info(self):
        """测试系统信息常量"""
        self.assertEqual(SYSTEM_VERSION, "1.0.0")
        self.assertEqual(SYSTEM_NAME, "DataStudio")
        self.assertIsInstance(SYSTEM_VERSION, str)
        self.assertIsInstance(SYSTEM_NAME, str)
    
    def test_serial_defaults(self):
        """测试串口默认配置"""
        self.assertEqual(SerialDefaults.BAUDRATE, 9600)
        self.assertEqual(SerialDefaults.BYTESIZE, 8)
        self.assertEqual(SerialDefaults.PARITY, 'N')
        self.assertEqual(SerialDefaults.STOPBITS, 1)
        self.assertEqual(SerialDefaults.TIMEOUT, 1.0)
        
        # 测试支持的波特率列表
        self.assertIn(9600, SerialDefaults.SUPPORTED_BAUDRATES)
        self.assertIn(115200, SerialDefaults.SUPPORTED_BAUDRATES)
        self.assertIsInstance(SerialDefaults.SUPPORTED_BAUDRATES, list)
        
        # 测试支持的数据位
        self.assertIn(8, SerialDefaults.SUPPORTED_BYTESIZES)
        self.assertIn(7, SerialDefaults.SUPPORTED_BYTESIZES)
        
        # 测试支持的校验位
        self.assertIn('N', SerialDefaults.SUPPORTED_PARITIES)
        self.assertIn('E', SerialDefaults.SUPPORTED_PARITIES)
    
    def test_buffer_defaults(self):
        """测试缓冲区默认配置"""
        self.assertEqual(BufferDefaults.BUFFER_SIZE, 4096)
        self.assertEqual(BufferDefaults.MAX_FRAME_SIZE, 1024)
        self.assertEqual(BufferDefaults.MIN_FRAME_SIZE, 4)
        self.assertEqual(BufferDefaults.BUFFER_WARNING_THRESHOLD, 0.8)
        self.assertEqual(BufferDefaults.BUFFER_CRITICAL_THRESHOLD, 0.95)
    
    def test_queue_defaults(self):
        """测试队列默认配置"""
        self.assertEqual(QueueDefaults.QUEUE_SIZE, 100)
        self.assertEqual(QueueDefaults.QUEUE_WARNING_THRESHOLD, 0.8)
        self.assertEqual(QueueDefaults.BATCH_PROCESS_SIZE, 10)
        self.assertEqual(QueueDefaults.QUEUE_TIMEOUT, 1.0)
    
    def test_data_types(self):
        """测试数据类型枚举"""
        # 测试枚举值
        self.assertEqual(DataTypes.INT8.value, "int8")
        self.assertEqual(DataTypes.UINT16.value, "uint16")
        self.assertEqual(DataTypes.FLOAT32.value, "float32")
        self.assertEqual(DataTypes.STRING.value, "string")
        
        # 测试数据类型大小映射
        self.assertEqual(DATA_TYPE_SIZES[DataTypes.INT8.value], 1)
        self.assertEqual(DATA_TYPE_SIZES[DataTypes.INT16.value], 2)
        self.assertEqual(DATA_TYPE_SIZES[DataTypes.INT32.value], 4)
        self.assertEqual(DATA_TYPE_SIZES[DataTypes.FLOAT32.value], 4)
        self.assertEqual(DATA_TYPE_SIZES[DataTypes.FLOAT64.value], 8)
        
        # 测试struct格式映射
        self.assertEqual(STRUCT_FORMAT_MAP[DataTypes.INT8.value], 'b')
        self.assertEqual(STRUCT_FORMAT_MAP[DataTypes.UINT16.value], 'H')
        self.assertEqual(STRUCT_FORMAT_MAP[DataTypes.FLOAT32.value], 'f')
    
    def test_byte_order(self):
        """测试字节序枚举"""
        self.assertEqual(ByteOrder.LITTLE_ENDIAN.value, "little")
        self.assertEqual(ByteOrder.BIG_ENDIAN.value, "big")
        
        # 测试字节序前缀映射
        self.assertEqual(BYTE_ORDER_PREFIX[ByteOrder.LITTLE_ENDIAN.value], '<')
        self.assertEqual(BYTE_ORDER_PREFIX[ByteOrder.BIG_ENDIAN.value], '>')
    
    def test_work_mode(self):
        """测试工作模式枚举"""
        self.assertEqual(WorkMode.SINGLE_COMMAND.value, "single_command")
        self.assertEqual(WorkMode.CONTINUOUS_COMMAND.value, "continuous_command")
        self.assertEqual(WorkMode.MIXED_MODE.value, "mixed_mode")
    
    def test_validation_strategy(self):
        """测试验证策略枚举"""
        self.assertEqual(ValidationStrategy.EXACT_MATCH.value, "exact_match")
        self.assertEqual(ValidationStrategy.REGEX_MATCH.value, "regex_match")
        self.assertEqual(ValidationStrategy.LENGTH_CHECK.value, "length_check")
        self.assertEqual(ValidationStrategy.CHECKSUM_VERIFY.value, "checksum_verify")
    
    def test_error_defaults(self):
        """测试错误处理默认配置"""
        self.assertEqual(ErrorDefaults.MAX_CONSECUTIVE_ERRORS, 5)
        self.assertEqual(ErrorDefaults.ERROR_RETRY_DELAY, 1.0)
        self.assertEqual(ErrorDefaults.MAX_RETRY_ATTEMPTS, 3)
        self.assertEqual(ErrorDefaults.ERROR_RECOVERY_TIMEOUT, 10.0)
    
    def test_error_type(self):
        """测试错误类型枚举"""
        self.assertEqual(ErrorType.COMMUNICATION_ERROR, 1)
        self.assertEqual(ErrorType.FRAME_DETECTION_ERROR, 2)
        self.assertEqual(ErrorType.DATA_PARSING_ERROR, 3)
        self.assertEqual(ErrorType.VALIDATION_ERROR, 4)
        self.assertEqual(ErrorType.CONFIGURATION_ERROR, 5)
        self.assertEqual(ErrorType.SYSTEM_ERROR, 6)
    
    def test_log_defaults(self):
        """测试日志默认配置"""
        self.assertEqual(LogDefaults.LOG_LEVEL, "INFO")
        self.assertIn("%(asctime)s", LogDefaults.LOG_FORMAT)
        self.assertIn("%(levelname)s", LogDefaults.LOG_FORMAT)
        self.assertEqual(LogDefaults.MAX_LOG_FILE_SIZE, 10 * 1024 * 1024)
        self.assertEqual(LogDefaults.MAX_LOG_FILES, 5)
        self.assertEqual(LogDefaults.LOG_ENCODING, "utf-8")
    
    def test_default_system_config(self):
        """测试默认系统配置"""
        self.assertIsInstance(DEFAULT_SYSTEM_CONFIG, dict)
        self.assertIn("system", DEFAULT_SYSTEM_CONFIG)
        self.assertIn("serial", DEFAULT_SYSTEM_CONFIG)
        self.assertIn("buffer", DEFAULT_SYSTEM_CONFIG)
        self.assertIn("queue", DEFAULT_SYSTEM_CONFIG)
        self.assertIn("error_handling", DEFAULT_SYSTEM_CONFIG)
        self.assertIn("logging", DEFAULT_SYSTEM_CONFIG)
        
        # 测试系统配置内容
        system_config = DEFAULT_SYSTEM_CONFIG["system"]
        self.assertEqual(system_config["name"], SYSTEM_NAME)
        self.assertEqual(system_config["version"], SYSTEM_VERSION)
        
        # 测试串口配置内容
        serial_config = DEFAULT_SYSTEM_CONFIG["serial"]
        self.assertEqual(serial_config["baudrate"], SerialDefaults.BAUDRATE)
        self.assertEqual(serial_config["bytesize"], SerialDefaults.BYTESIZE)


if __name__ == '__main__':
    unittest.main()
