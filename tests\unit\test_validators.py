#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证器模块单元测试

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import unittest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.validators import (
    SerialConfigValidator, ProtocolConfigValidator, SystemConfigValidator,
    validate_serial_config, validate_protocol_config, validate_system_config
)
from utils.exceptions import ConfigValidationError


class TestSerialConfigValidator(unittest.TestCase):
    """串口配置验证器测试"""
    
    def setUp(self):
        self.validator = SerialConfigValidator()
    
    def test_valid_serial_config(self):
        """测试有效的串口配置"""
        config = {
            "port": "COM1",
            "baudrate": 9600,
            "bytesize": 8,
            "parity": "N",
            "stopbits": 1,
            "timeout": 1.0
        }
        
        result = self.validator.validate(config)
        self.assertTrue(result)
        self.assertFalse(self.validator.has_errors())
    
    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        config = {
            "port": "COM1",
            "baudrate": 9600
            # 缺少其他必需字段
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
        
        errors = self.validator.get_errors()
        self.assertIn("缺少必需字段: bytesize", errors)
        self.assertIn("缺少必需字段: parity", errors)
    
    def test_invalid_baudrate(self):
        """测试无效波特率"""
        config = {
            "port": "COM1",
            "baudrate": 1234,  # 不支持的波特率
            "bytesize": 8,
            "parity": "N",
            "stopbits": 1,
            "timeout": 1.0
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
        
        errors = self.validator.get_errors()
        self.assertIn("无效的波特率: 1234", errors)
    
    def test_invalid_parity(self):
        """测试无效校验位"""
        config = {
            "port": "COM1",
            "baudrate": 9600,
            "bytesize": 8,
            "parity": "X",  # 无效校验位
            "stopbits": 1,
            "timeout": 1.0
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())


class TestProtocolConfigValidator(unittest.TestCase):
    """协议配置验证器测试"""
    
    def setUp(self):
        self.validator = ProtocolConfigValidator()
    
    def test_valid_protocol_config(self):
        """测试有效的协议配置"""
        config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "frame_detection": {
                "header": "AA BB",
                "footer": "CC DD",
                "length": {
                    "min_length": 8,
                    "max_length": 256
                }
            },
            "data_fields": [
                {
                    "name": "temperature",
                    "type": "int16",
                    "offset": 4,
                    "byte_order": "little"
                }
            ],
            "commands": {
                "READ_DATA": {
                    "data": "01 03 00 00",
                    "response_validation": {
                        "strategy": "exact_match",
                        "expected_response": "01 03 02"
                    }
                }
            },
            "work_mode": {
                "mode": "continuous_command",
                "interval": 1.0
            }
        }
        
        result = self.validator.validate(config)
        self.assertTrue(result)
        self.assertFalse(self.validator.has_errors())
    
    def test_missing_basic_info(self):
        """测试缺少基本信息"""
        config = {
            "version": "1.0.0",
            "description": "测试协议描述"
            # 缺少name字段
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
        
        errors = self.validator.get_errors()
        self.assertIn("缺少必需字段: name", errors)
    
    def test_invalid_data_field_type(self):
        """测试无效的数据字段类型"""
        config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "data_fields": [
                {
                    "name": "test_field",
                    "type": "invalid_type",  # 无效类型
                    "offset": 0
                }
            ]
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
        
        errors = self.validator.get_errors()
        self.assertTrue(any("类型无效" in error for error in errors))
    
    def test_duplicate_field_names(self):
        """测试重复的字段名称"""
        config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "data_fields": [
                {
                    "name": "temperature",
                    "type": "int16",
                    "offset": 0
                },
                {
                    "name": "temperature",  # 重复名称
                    "type": "int16",
                    "offset": 2
                }
            ]
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
        
        errors = self.validator.get_errors()
        self.assertTrue(any("名称重复" in error for error in errors))
    
    def test_invalid_regex_pattern(self):
        """测试无效的正则表达式"""
        config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "commands": {
                "TEST_CMD": {
                    "data": "01 02 03",
                    "response_validation": {
                        "strategy": "regex_match",
                        "pattern": "[invalid regex"  # 无效正则表达式
                    }
                }
            }
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())


class TestSystemConfigValidator(unittest.TestCase):
    """系统配置验证器测试"""
    
    def setUp(self):
        self.validator = SystemConfigValidator()
    
    def test_valid_system_config(self):
        """测试有效的系统配置"""
        config = {
            "buffer": {
                "size": 4096,
                "warning_threshold": 0.8
            },
            "queue": {
                "size": 100,
                "warning_threshold": 0.8,
                "batch_size": 10
            },
            "error_handling": {
                "max_consecutive_errors": 5,
                "retry_delay": 1.0,
                "max_retry_attempts": 3
            },
            "logging": {
                "level": "INFO",
                "max_file_size": 10485760,
                "max_files": 5
            }
        }
        
        result = self.validator.validate(config)
        self.assertTrue(result)
        self.assertFalse(self.validator.has_errors())
    
    def test_invalid_buffer_size(self):
        """测试无效的缓冲区大小"""
        config = {
            "buffer": {
                "size": 2  # 太小
            }
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
    
    def test_invalid_threshold(self):
        """测试无效的阈值"""
        config = {
            "queue": {
                "warning_threshold": 1.5  # 超出范围
            }
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())
    
    def test_invalid_log_level(self):
        """测试无效的日志级别"""
        config = {
            "logging": {
                "level": "INVALID_LEVEL"
            }
        }
        
        result = self.validator.validate(config)
        self.assertFalse(result)
        self.assertTrue(self.validator.has_errors())


class TestConvenienceFunctions(unittest.TestCase):
    """便捷函数测试"""
    
    def test_validate_serial_config_success(self):
        """测试串口配置验证成功"""
        config = {
            "port": "COM1",
            "baudrate": 9600,
            "bytesize": 8,
            "parity": "N",
            "stopbits": 1,
            "timeout": 1.0
        }
        
        # 应该不抛出异常
        validate_serial_config(config)
    
    def test_validate_serial_config_failure(self):
        """测试串口配置验证失败"""
        config = {
            "port": "COM1",
            "baudrate": 1234  # 无效波特率
            # 缺少其他字段
        }
        
        with self.assertRaises(ConfigValidationError):
            validate_serial_config(config)
    
    def test_validate_protocol_config_success(self):
        """测试协议配置验证成功"""
        config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述"
        }
        
        # 应该不抛出异常
        validate_protocol_config(config)
    
    def test_validate_system_config_success(self):
        """测试系统配置验证成功"""
        config = {
            "buffer": {
                "size": 4096
            }
        }
        
        # 应该不抛出异常
        validate_system_config(config)


if __name__ == '__main__':
    unittest.main()
