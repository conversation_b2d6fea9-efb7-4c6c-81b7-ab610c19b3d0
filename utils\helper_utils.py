#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助工具函数
提供十六进制处理、数据转换、时间戳等通用工具函数

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import re
import struct
import time
from datetime import datetime
from typing import Union, List, Optional, Any, Dict
from pathlib import Path

from .constants import DataTypes, ByteOrder, STRUCT_FORMAT_MAP, BYTE_ORDER_PREFIX


class HexUtils:
    """十六进制处理工具类"""
    
    @staticmethod
    def bytes_to_hex_string(data: bytes, separator: str = " ") -> str:
        """
        将字节数据转换为十六进制字符串
        
        Args:
            data: 字节数据
            separator: 分隔符，默认为空格
            
        Returns:
            十六进制字符串，如 "01 02 03 FF"
        """
        if not isinstance(data, bytes):
            raise TypeError("输入数据必须是bytes类型")
        
        hex_list = [f"{byte:02X}" for byte in data]
        return separator.join(hex_list)
    
    @staticmethod
    def hex_string_to_bytes(hex_string: str) -> bytes:
        """
        将十六进制字符串转换为字节数据
        
        Args:
            hex_string: 十六进制字符串，如 "01 02 03 FF" 或 "010203FF"
            
        Returns:
            字节数据
        """
        if not isinstance(hex_string, str):
            raise TypeError("输入必须是字符串类型")
        
        # 移除所有非十六进制字符
        clean_hex = re.sub(r'[^0-9A-Fa-f]', '', hex_string)
        
        # 确保长度为偶数
        if len(clean_hex) % 2 != 0:
            raise ValueError("十六进制字符串长度必须为偶数")
        
        try:
            return bytes.fromhex(clean_hex)
        except ValueError as e:
            raise ValueError(f"无效的十六进制字符串: {hex_string}") from e
    
    @staticmethod
    def is_valid_hex_string(hex_string: str) -> bool:
        """
        检查是否为有效的十六进制字符串
        
        Args:
            hex_string: 待检查的字符串
            
        Returns:
            是否为有效的十六进制字符串
        """
        if not isinstance(hex_string, str):
            return False
        
        # 移除所有非十六进制字符
        clean_hex = re.sub(r'[^0-9A-Fa-f]', '', hex_string)
        
        # 检查长度和内容
        return len(clean_hex) > 0 and len(clean_hex) % 2 == 0
    
    @staticmethod
    def calculate_checksum(data: bytes, checksum_type: str = "xor") -> int:
        """
        计算数据校验和
        
        Args:
            data: 字节数据
            checksum_type: 校验和类型，支持 "xor", "sum", "crc8"
            
        Returns:
            校验和值
        """
        if not isinstance(data, bytes):
            raise TypeError("输入数据必须是bytes类型")
        
        if checksum_type == "xor":
            checksum = 0
            for byte in data:
                checksum ^= byte
            return checksum & 0xFF
        
        elif checksum_type == "sum":
            return sum(data) & 0xFF
        
        elif checksum_type == "crc8":
            # 简单的CRC8实现
            crc = 0
            for byte in data:
                crc ^= byte
                for _ in range(8):
                    if crc & 0x80:
                        crc = (crc << 1) ^ 0x07
                    else:
                        crc <<= 1
                    crc &= 0xFF
            return crc
        
        else:
            raise ValueError(f"不支持的校验和类型: {checksum_type}")


class DataConverter:
    """数据类型转换工具类"""
    
    @staticmethod
    def parse_value_by_type(data: bytes, data_type: str, byte_order: str = "little") -> Any:
        """
        根据数据类型解析字节数据
        
        Args:
            data: 字节数据
            data_type: 数据类型，如 "int16", "float32"
            byte_order: 字节序，"little" 或 "big"
            
        Returns:
            解析后的值
        """
        if not isinstance(data, bytes):
            raise TypeError("输入数据必须是bytes类型")
        
        if data_type not in STRUCT_FORMAT_MAP:
            if data_type == DataTypes.STRING.value:
                return data.decode('utf-8', errors='ignore')
            elif data_type == DataTypes.BYTES.value:
                return data
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
        
        # 获取struct格式
        format_char = STRUCT_FORMAT_MAP[data_type]
        byte_order_prefix = BYTE_ORDER_PREFIX.get(byte_order, '<')
        format_string = byte_order_prefix + format_char
        
        try:
            return struct.unpack(format_string, data)[0]
        except struct.error as e:
            raise ValueError(f"数据解析失败: {e}")
    
    @staticmethod
    def pack_value_by_type(value: Any, data_type: str, byte_order: str = "little") -> bytes:
        """
        根据数据类型打包值为字节数据
        
        Args:
            value: 要打包的值
            data_type: 数据类型
            byte_order: 字节序
            
        Returns:
            打包后的字节数据
        """
        if data_type not in STRUCT_FORMAT_MAP:
            if data_type == DataTypes.STRING.value:
                return str(value).encode('utf-8')
            elif data_type == DataTypes.BYTES.value:
                return bytes(value) if not isinstance(value, bytes) else value
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
        
        # 获取struct格式
        format_char = STRUCT_FORMAT_MAP[data_type]
        byte_order_prefix = BYTE_ORDER_PREFIX.get(byte_order, '<')
        format_string = byte_order_prefix + format_char
        
        try:
            return struct.pack(format_string, value)
        except struct.error as e:
            raise ValueError(f"数据打包失败: {e}")
    
    @staticmethod
    def apply_scaling(value: Union[int, float], scale: float = 1.0, offset: float = 0.0) -> float:
        """
        应用缩放和偏移
        
        Args:
            value: 原始值
            scale: 缩放因子
            offset: 偏移量
            
        Returns:
            缩放后的值 = (原始值 * 缩放因子) + 偏移量
        """
        return (float(value) * scale) + offset


class TimeUtils:
    """时间处理工具类"""
    
    @staticmethod
    def get_timestamp() -> float:
        """获取当前时间戳"""
        return time.time()
    
    @staticmethod
    def get_formatted_time(timestamp: Optional[float] = None, 
                          format_string: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        获取格式化的时间字符串
        
        Args:
            timestamp: 时间戳，None表示当前时间
            format_string: 时间格式字符串
            
        Returns:
            格式化的时间字符串
        """
        if timestamp is None:
            timestamp = time.time()
        
        return datetime.fromtimestamp(timestamp).strftime(format_string)
    
    @staticmethod
    def get_milliseconds() -> int:
        """获取当前毫秒时间戳"""
        return int(time.time() * 1000)


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory_exists(directory_path: Union[str, Path]) -> Path:
        """
        确保目录存在，不存在则创建
        
        Args:
            directory_path: 目录路径
            
        Returns:
            Path对象
        """
        path = Path(directory_path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """
        获取安全的文件名（移除非法字符）
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 移除或替换非法字符
        safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除前后空格和点
        safe_chars = safe_chars.strip(' .')
        # 确保不为空
        return safe_chars if safe_chars else "unnamed"
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        获取文件大小（字节）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小，文件不存在返回0
        """
        path = Path(file_path)
        return path.stat().st_size if path.exists() else 0


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def is_valid_port_number(port: Union[str, int]) -> bool:
        """
        验证端口号是否有效
        
        Args:
            port: 端口号
            
        Returns:
            是否为有效端口号
        """
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_baudrate(baudrate: Union[str, int]) -> bool:
        """
        验证波特率是否有效
        
        Args:
            baudrate: 波特率
            
        Returns:
            是否为有效波特率
        """
        from .constants import SerialDefaults
        
        try:
            baudrate_num = int(baudrate)
            return baudrate_num in SerialDefaults.SUPPORTED_BAUDRATES
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_timeout(timeout: Union[str, int, float]) -> bool:
        """
        验证超时时间是否有效
        
        Args:
            timeout: 超时时间
            
        Returns:
            是否为有效超时时间
        """
        try:
            timeout_num = float(timeout)
            return 0.1 <= timeout_num <= 60.0
        except (ValueError, TypeError):
            return False


class StringUtils:
    """字符串处理工具类"""
    
    @staticmethod
    def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
        """
        截断字符串到指定长度
        
        Args:
            text: 原始字符串
            max_length: 最大长度
            suffix: 截断后缀
            
        Returns:
            截断后的字符串
        """
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def format_bytes_size(size_bytes: int) -> str:
        """
        格式化字节大小为可读字符串
        
        Args:
            size_bytes: 字节大小
            
        Returns:
            格式化的大小字符串，如 "1.5 KB", "2.3 MB"
        """
        if size_bytes == 0:
            return "0 B"
        
        units = ["B", "KB", "MB", "GB", "TB"]
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"


# 便捷函数导出
def bytes_to_hex(data: bytes, separator: str = " ") -> str:
    """便捷函数：字节转十六进制字符串"""
    return HexUtils.bytes_to_hex_string(data, separator)


def hex_to_bytes(hex_string: str) -> bytes:
    """便捷函数：十六进制字符串转字节"""
    return HexUtils.hex_string_to_bytes(hex_string)


def get_timestamp() -> float:
    """便捷函数：获取时间戳"""
    return TimeUtils.get_timestamp()


def get_formatted_time(timestamp: Optional[float] = None) -> str:
    """便捷函数：获取格式化时间"""
    return TimeUtils.get_formatted_time(timestamp)
