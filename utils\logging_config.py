#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
提供系统日志的配置和管理功能

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any

from .constants import LogDefaults
from .helper_utils import FileUtils


class LoggingConfig:
    """日志配置管理类"""
    
    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._configured = False
    
    def setup_logging(self, 
                     log_level: str = LogDefaults.LOG_LEVEL,
                     log_dir: str = "logs",
                     console_output: bool = True,
                     file_output: bool = True,
                     max_file_size: int = LogDefaults.MAX_LOG_FILE_SIZE,
                     max_files: int = LogDefaults.MAX_LOG_FILES) -> None:
        """
        设置系统日志配置
        
        Args:
            log_level: 日志级别
            log_dir: 日志目录
            console_output: 是否输出到控制台
            file_output: 是否输出到文件
            max_file_size: 最大文件大小
            max_files: 最大文件数量
        """
        if self._configured:
            return
        
        # 确保日志目录存在
        log_path = FileUtils.ensure_directory_exists(log_dir)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            fmt=LogDefaults.LOG_FORMAT,
            datefmt=LogDefaults.LOG_DATE_FORMAT
        )
        
        # 控制台处理器
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, log_level.upper()))
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if file_output:
            # 主日志文件
            main_log_file = log_path / "datastudio.log"
            file_handler = logging.handlers.RotatingFileHandler(
                filename=main_log_file,
                maxBytes=max_file_size,
                backupCount=max_files,
                encoding=LogDefaults.LOG_ENCODING
            )
            file_handler.setLevel(getattr(logging, log_level.upper()))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = log_path / "error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                filename=error_log_file,
                maxBytes=max_file_size,
                backupCount=max_files,
                encoding=LogDefaults.LOG_ENCODING
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            root_logger.addHandler(error_handler)
        
        self._configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器对象
        """
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        
        return self._loggers[name]
    
    def create_component_logger(self, component_name: str, 
                              log_file: Optional[str] = None) -> logging.Logger:
        """
        为特定组件创建专用日志器
        
        Args:
            component_name: 组件名称
            log_file: 专用日志文件名
            
        Returns:
            组件专用日志器
        """
        logger = logging.getLogger(f"datastudio.{component_name}")
        
        if log_file and not logger.handlers:
            # 确保日志目录存在
            log_path = FileUtils.ensure_directory_exists("logs")
            
            # 创建组件专用文件处理器
            component_log_file = log_path / log_file
            handler = logging.handlers.RotatingFileHandler(
                filename=component_log_file,
                maxBytes=LogDefaults.MAX_LOG_FILE_SIZE,
                backupCount=LogDefaults.MAX_LOG_FILES,
                encoding=LogDefaults.LOG_ENCODING
            )
            
            formatter = logging.Formatter(
                fmt=LogDefaults.LOG_FORMAT,
                datefmt=LogDefaults.LOG_DATE_FORMAT
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
            # 防止日志传播到根日志器（避免重复记录）
            logger.propagate = False
        
        self._loggers[component_name] = logger
        return logger
    
    def set_log_level(self, level: str, logger_name: Optional[str] = None) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别
            logger_name: 日志器名称，None表示根日志器
        """
        if logger_name:
            logger = self.get_logger(logger_name)
        else:
            logger = logging.getLogger()
        
        logger.setLevel(getattr(logging, level.upper()))
    
    def is_configured(self) -> bool:
        """检查日志是否已配置"""
        return self._configured


# 全局日志配置实例
_logging_config = LoggingConfig()


def setup_logging(**kwargs) -> None:
    """设置系统日志配置的便捷函数"""
    _logging_config.setup_logging(**kwargs)


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return _logging_config.get_logger(name)


def create_component_logger(component_name: str, 
                          log_file: Optional[str] = None) -> logging.Logger:
    """创建组件日志器的便捷函数"""
    return _logging_config.create_component_logger(component_name, log_file)


# 预定义的组件日志器
def get_serial_logger() -> logging.Logger:
    """获取串口通信日志器"""
    return create_component_logger("serial", "serial.log")


def get_data_processing_logger() -> logging.Logger:
    """获取数据处理日志器"""
    return create_component_logger("data_processing", "data_processing.log")


def get_business_logic_logger() -> logging.Logger:
    """获取业务逻辑日志器"""
    return create_component_logger("business_logic", "business_logic.log")


def get_ui_logger() -> logging.Logger:
    """获取用户界面日志器"""
    return create_component_logger("ui", "ui.log")


def get_config_logger() -> logging.Logger:
    """获取配置管理日志器"""
    return create_component_logger("config", "config.log")
