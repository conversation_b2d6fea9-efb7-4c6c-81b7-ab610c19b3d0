#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
提供动态配置管理和验证功能

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import json
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

from .constants import DEFAULT_SYSTEM_CONFIG, DEFAULT_CONFIG_DIR, DEFAULT_PROTOCOLS_DIR
from .exceptions import (
    ConfigFileNotFoundError, ConfigParsingError, ConfigValidationError
)
from .validators import (
    validate_serial_config, validate_protocol_config, validate_system_config
)
from .logging_config import get_config_logger


@dataclass
class SerialConfig:
    """串口配置数据类"""
    port: str
    baudrate: int = 9600
    bytesize: int = 8
    parity: str = 'N'
    stopbits: float = 1
    timeout: float = 1.0
    write_timeout: float = 1.0


@dataclass
class FrameDetectionConfig:
    """帧检测配置数据类"""
    header: Optional[str] = None
    footer: Optional[str] = None
    min_length: int = 4
    max_length: int = 1024
    length_field_offset: Optional[int] = None
    length_field_size: int = 2
    length_includes_header: bool = False


@dataclass
class DataFieldConfig:
    """数据字段配置数据类"""
    name: str
    type: str
    offset: int
    byte_order: str = "little"
    scale: float = 1.0
    offset_value: float = 0.0
    unit: str = ""
    description: str = ""


@dataclass
class ResponseValidationConfig:
    """应答验证配置数据类"""
    strategy: str
    expected_response: Optional[str] = None
    pattern: Optional[str] = None
    timeout: float = 1.0
    max_retries: int = 3


@dataclass
class CommandConfig:
    """指令配置数据类"""
    name: str
    data: str
    description: str = ""
    response_validation: Optional[ResponseValidationConfig] = None
    delay_after: float = 0.0


@dataclass
class WorkModeConfig:
    """工作模式配置数据类"""
    mode: str
    interval: float = 1.0
    commands: List[str] = field(default_factory=list)


@dataclass
class ProtocolConfig:
    """协议配置数据类"""
    name: str
    version: str
    description: str
    serial: SerialConfig
    frame_detection: FrameDetectionConfig
    data_fields: List[DataFieldConfig] = field(default_factory=list)
    commands: Dict[str, CommandConfig] = field(default_factory=dict)
    work_mode: Optional[WorkModeConfig] = None


class ConfigManager:
    """配置管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._logger = get_config_logger()
        self._system_config: Dict[str, Any] = DEFAULT_SYSTEM_CONFIG.copy()
        self._protocol_config: Optional[ProtocolConfig] = None
        self._config_file_path: Optional[Path] = None
        self._lock = threading.RLock()
    
    def load_system_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        加载系统配置
        
        Args:
            config_path: 配置文件路径，None表示使用默认配置
            
        Returns:
            系统配置字典
        """
        with self._lock:
            if config_path:
                try:
                    config_file = Path(config_path)
                    if not config_file.exists():
                        raise ConfigFileNotFoundError(str(config_file))
                    
                    with open(config_file, 'r', encoding='utf-8') as f:
                        loaded_config = json.load(f)
                    
                    # 验证配置
                    validate_system_config(loaded_config)
                    
                    # 合并配置（用加载的配置覆盖默认配置）
                    self._merge_config(self._system_config, loaded_config)
                    
                    self._logger.info(f"系统配置加载成功: {config_file}")
                    
                except json.JSONDecodeError as e:
                    raise ConfigParsingError(f"JSON解析失败: {e}", str(config_file))
                except Exception as e:
                    self._logger.error(f"加载系统配置失败: {e}")
                    raise
            
            return self._system_config.copy()
    
    def load_protocol_config(self, config_path: str) -> ProtocolConfig:
        """
        加载协议配置
        
        Args:
            config_path: 协议配置文件路径
            
        Returns:
            协议配置对象
        """
        with self._lock:
            config_file = Path(config_path)
            if not config_file.exists():
                raise ConfigFileNotFoundError(str(config_file))
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 验证配置
                validate_protocol_config(config_data)
                
                # 转换为配置对象
                self._protocol_config = self._parse_protocol_config(config_data)
                self._config_file_path = config_file
                
                self._logger.info(f"协议配置加载成功: {config_file}")
                return self._protocol_config
                
            except json.JSONDecodeError as e:
                raise ConfigParsingError(f"JSON解析失败: {e}", str(config_file))
            except Exception as e:
                self._logger.error(f"加载协议配置失败: {e}")
                raise
    
    def _parse_protocol_config(self, config_data: Dict[str, Any]) -> ProtocolConfig:
        """解析协议配置数据"""
        # 解析串口配置
        serial_data = config_data.get('serial', {})
        serial_config = SerialConfig(**serial_data)
        
        # 解析帧检测配置
        frame_data = config_data.get('frame_detection', {})
        frame_config = FrameDetectionConfig(**frame_data)
        
        # 解析数据字段配置
        data_fields = []
        for field_data in config_data.get('data_fields', []):
            data_fields.append(DataFieldConfig(**field_data))
        
        # 解析指令配置
        commands = {}
        for cmd_name, cmd_data in config_data.get('commands', {}).items():
            # 解析应答验证配置
            response_validation = None
            if 'response_validation' in cmd_data:
                rv_data = cmd_data['response_validation']
                response_validation = ResponseValidationConfig(**rv_data)
            
            cmd_config = CommandConfig(
                name=cmd_name,
                data=cmd_data['data'],
                description=cmd_data.get('description', ''),
                response_validation=response_validation,
                delay_after=cmd_data.get('delay_after', 0.0)
            )
            commands[cmd_name] = cmd_config
        
        # 解析工作模式配置
        work_mode = None
        if 'work_mode' in config_data:
            wm_data = config_data['work_mode']
            work_mode = WorkModeConfig(**wm_data)
        
        return ProtocolConfig(
            name=config_data['name'],
            version=config_data['version'],
            description=config_data['description'],
            serial=serial_config,
            frame_detection=frame_config,
            data_fields=data_fields,
            commands=commands,
            work_mode=work_mode
        )
    
    def _merge_config(self, base_config: Dict[str, Any], 
                     new_config: Dict[str, Any]) -> None:
        """递归合并配置"""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        with self._lock:
            return self._system_config.copy()
    
    def get_protocol_config(self) -> Optional[ProtocolConfig]:
        """获取协议配置"""
        with self._lock:
            return self._protocol_config
    
    def get_serial_config(self) -> Optional[SerialConfig]:
        """获取串口配置"""
        with self._lock:
            return self._protocol_config.serial if self._protocol_config else None
    
    def get_frame_detection_config(self) -> Optional[FrameDetectionConfig]:
        """获取帧检测配置"""
        with self._lock:
            return self._protocol_config.frame_detection if self._protocol_config else None
    
    def get_data_fields_config(self) -> List[DataFieldConfig]:
        """获取数据字段配置"""
        with self._lock:
            return self._protocol_config.data_fields if self._protocol_config else []
    
    def get_commands_config(self) -> Dict[str, CommandConfig]:
        """获取指令配置"""
        with self._lock:
            return self._protocol_config.commands if self._protocol_config else {}
    
    def get_work_mode_config(self) -> Optional[WorkModeConfig]:
        """获取工作模式配置"""
        with self._lock:
            return self._protocol_config.work_mode if self._protocol_config else None
    
    def get_config_file_path(self) -> Optional[Path]:
        """获取当前配置文件路径"""
        with self._lock:
            return self._config_file_path
    
    def is_protocol_loaded(self) -> bool:
        """检查是否已加载协议配置"""
        with self._lock:
            return self._protocol_config is not None
    
    def reload_protocol_config(self) -> Optional[ProtocolConfig]:
        """重新加载协议配置"""
        with self._lock:
            if self._config_file_path:
                return self.load_protocol_config(str(self._config_file_path))
            return None
    
    def list_available_protocols(self, protocols_dir: Optional[str] = None) -> List[str]:
        """
        列出可用的协议配置文件
        
        Args:
            protocols_dir: 协议配置目录，None表示使用默认目录
            
        Returns:
            协议配置文件列表
        """
        if protocols_dir is None:
            protocols_dir = DEFAULT_PROTOCOLS_DIR
        
        protocols_path = Path(protocols_dir)
        if not protocols_path.exists():
            return []
        
        protocol_files = []
        for file_path in protocols_path.glob("*.json"):
            protocol_files.append(file_path.name)
        
        return sorted(protocol_files)
    
    def create_default_protocol_template(self, output_path: str) -> None:
        """
        创建默认协议配置模板
        
        Args:
            output_path: 输出文件路径
        """
        template = {
            "name": "示例协议",
            "version": "1.0.0",
            "description": "这是一个示例协议配置文件",
            "serial": {
                "port": "COM1",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 1.0
            },
            "frame_detection": {
                "header": "AA BB",
                "footer": "CC DD",
                "min_length": 8,
                "max_length": 256
            },
            "data_fields": [
                {
                    "name": "temperature",
                    "type": "int16",
                    "offset": 4,
                    "byte_order": "little",
                    "scale": 0.1,
                    "offset_value": 0.0,
                    "unit": "°C",
                    "description": "温度值"
                }
            ],
            "commands": {
                "READ_DATA": {
                    "data": "01 03 00 00 00 01",
                    "description": "读取数据",
                    "response_validation": {
                        "strategy": "exact_match",
                        "expected_response": "01 03 02",
                        "timeout": 2.0,
                        "max_retries": 3
                    }
                }
            },
            "work_mode": {
                "mode": "continuous_command",
                "interval": 1.0,
                "commands": ["READ_DATA"]
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        self._logger.info(f"协议配置模板已创建: {output_file}")


# 全局配置管理器实例
_config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    return _config_manager
