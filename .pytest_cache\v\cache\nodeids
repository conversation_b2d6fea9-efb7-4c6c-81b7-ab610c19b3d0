["tests/unit/test_constants.py::TestConstants::test_buffer_defaults", "tests/unit/test_constants.py::TestConstants::test_byte_order", "tests/unit/test_constants.py::TestConstants::test_data_types", "tests/unit/test_constants.py::TestConstants::test_default_system_config", "tests/unit/test_constants.py::TestConstants::test_error_defaults", "tests/unit/test_constants.py::TestConstants::test_error_type", "tests/unit/test_constants.py::TestConstants::test_log_defaults", "tests/unit/test_constants.py::TestConstants::test_queue_defaults", "tests/unit/test_constants.py::TestConstants::test_serial_defaults", "tests/unit/test_constants.py::TestConstants::test_system_info", "tests/unit/test_constants.py::TestConstants::test_validation_strategy", "tests/unit/test_constants.py::TestConstants::test_work_mode", "tests/unit/test_exceptions.py::TestBusinessLogicExceptions::test_command_execution_error", "tests/unit/test_exceptions.py::TestBusinessLogicExceptions::test_protocol_flow_error", "tests/unit/test_exceptions.py::TestCommunicationExceptions::test_serial_connection_error", "tests/unit/test_exceptions.py::TestCommunicationExceptions::test_serial_timeout_error", "tests/unit/test_exceptions.py::TestConfigurationExceptions::test_config_file_not_found_error", "tests/unit/test_exceptions.py::TestConfigurationExceptions::test_config_validation_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_data_parsing_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_frame_detection_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_validation_error", "tests/unit/test_exceptions.py::TestDataStudioException::test_basic_exception", "tests/unit/test_exceptions.py::TestDataStudioException::test_exception_with_code", "tests/unit/test_exceptions.py::TestDataStudioException::test_exception_with_details", "tests/unit/test_exceptions.py::TestDataStudioException::test_to_dict", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_format_exception_info", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_get_error_severity", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_is_recoverable_error", "tests/unit/test_exceptions.py::TestSystemExceptions::test_initialization_error", "tests/unit/test_helper_utils.py::TestConvenienceFunctions::test_convenience_functions", "tests/unit/test_helper_utils.py::TestDataConverter::test_apply_scaling", "tests/unit/test_helper_utils.py::TestDataConverter::test_pack_value_by_type", "tests/unit/test_helper_utils.py::TestDataConverter::test_parse_value_by_type", "tests/unit/test_helper_utils.py::TestHexUtils::test_bytes_to_hex_string", "tests/unit/test_helper_utils.py::TestHexUtils::test_calculate_checksum", "tests/unit/test_helper_utils.py::TestHexUtils::test_hex_string_to_bytes", "tests/unit/test_helper_utils.py::TestHexUtils::test_is_valid_hex_string", "tests/unit/test_helper_utils.py::TestStringUtils::test_format_bytes_size", "tests/unit/test_helper_utils.py::TestStringUtils::test_truncate_string", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_formatted_time", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_milliseconds", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_timestamp", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_baudrate", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_port_number", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_timeout"]