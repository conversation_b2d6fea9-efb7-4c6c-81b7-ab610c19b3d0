["tests/unit/test_config_manager.py::TestConfigManager::test_create_default_protocol_template", "tests/unit/test_config_manager.py::TestConfigManager::test_get_config_methods", "tests/unit/test_config_manager.py::TestConfigManager::test_list_available_protocols", "tests/unit/test_config_manager.py::TestConfigManager::test_load_protocol_config", "tests/unit/test_config_manager.py::TestConfigManager::test_load_system_config_default", "tests/unit/test_config_manager.py::TestConfigManager::test_load_system_config_file_not_found", "tests/unit/test_config_manager.py::TestConfigManager::test_load_system_config_from_file", "tests/unit/test_config_manager.py::TestConfigManager::test_load_system_config_invalid_json", "tests/unit/test_config_manager.py::TestConfigManager::test_singleton_pattern", "tests/unit/test_config_manager.py::TestDataClasses::test_data_field_config", "tests/unit/test_config_manager.py::TestDataClasses::test_frame_detection_config", "tests/unit/test_config_manager.py::TestDataClasses::test_serial_config", "tests/unit/test_constants.py::TestConstants::test_buffer_defaults", "tests/unit/test_constants.py::TestConstants::test_byte_order", "tests/unit/test_constants.py::TestConstants::test_data_types", "tests/unit/test_constants.py::TestConstants::test_default_system_config", "tests/unit/test_constants.py::TestConstants::test_error_defaults", "tests/unit/test_constants.py::TestConstants::test_error_type", "tests/unit/test_constants.py::TestConstants::test_log_defaults", "tests/unit/test_constants.py::TestConstants::test_queue_defaults", "tests/unit/test_constants.py::TestConstants::test_serial_defaults", "tests/unit/test_constants.py::TestConstants::test_system_info", "tests/unit/test_constants.py::TestConstants::test_validation_strategy", "tests/unit/test_constants.py::TestConstants::test_work_mode", "tests/unit/test_exceptions.py::TestBusinessLogicExceptions::test_command_execution_error", "tests/unit/test_exceptions.py::TestBusinessLogicExceptions::test_protocol_flow_error", "tests/unit/test_exceptions.py::TestCommunicationExceptions::test_serial_connection_error", "tests/unit/test_exceptions.py::TestCommunicationExceptions::test_serial_timeout_error", "tests/unit/test_exceptions.py::TestConfigurationExceptions::test_config_file_not_found_error", "tests/unit/test_exceptions.py::TestConfigurationExceptions::test_config_validation_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_data_parsing_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_frame_detection_error", "tests/unit/test_exceptions.py::TestDataProcessingExceptions::test_validation_error", "tests/unit/test_exceptions.py::TestDataStudioException::test_basic_exception", "tests/unit/test_exceptions.py::TestDataStudioException::test_exception_with_code", "tests/unit/test_exceptions.py::TestDataStudioException::test_exception_with_details", "tests/unit/test_exceptions.py::TestDataStudioException::test_to_dict", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_format_exception_info", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_get_error_severity", "tests/unit/test_exceptions.py::TestExceptionUtilities::test_is_recoverable_error", "tests/unit/test_exceptions.py::TestSystemExceptions::test_initialization_error", "tests/unit/test_helper_utils.py::TestConvenienceFunctions::test_convenience_functions", "tests/unit/test_helper_utils.py::TestDataConverter::test_apply_scaling", "tests/unit/test_helper_utils.py::TestDataConverter::test_pack_value_by_type", "tests/unit/test_helper_utils.py::TestDataConverter::test_parse_value_by_type", "tests/unit/test_helper_utils.py::TestHexUtils::test_bytes_to_hex_string", "tests/unit/test_helper_utils.py::TestHexUtils::test_calculate_checksum", "tests/unit/test_helper_utils.py::TestHexUtils::test_hex_string_to_bytes", "tests/unit/test_helper_utils.py::TestHexUtils::test_is_valid_hex_string", "tests/unit/test_helper_utils.py::TestStringUtils::test_format_bytes_size", "tests/unit/test_helper_utils.py::TestStringUtils::test_truncate_string", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_formatted_time", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_milliseconds", "tests/unit/test_helper_utils.py::TestTimeUtils::test_get_timestamp", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_baudrate", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_port_number", "tests/unit/test_helper_utils.py::TestValidationUtils::test_is_valid_timeout", "tests/unit/test_validators.py::TestConvenienceFunctions::test_validate_protocol_config_success", "tests/unit/test_validators.py::TestConvenienceFunctions::test_validate_serial_config_failure", "tests/unit/test_validators.py::TestConvenienceFunctions::test_validate_serial_config_success", "tests/unit/test_validators.py::TestConvenienceFunctions::test_validate_system_config_success", "tests/unit/test_validators.py::TestProtocolConfigValidator::test_duplicate_field_names", "tests/unit/test_validators.py::TestProtocolConfigValidator::test_invalid_data_field_type", "tests/unit/test_validators.py::TestProtocolConfigValidator::test_invalid_regex_pattern", "tests/unit/test_validators.py::TestProtocolConfigValidator::test_missing_basic_info", "tests/unit/test_validators.py::TestProtocolConfigValidator::test_valid_protocol_config", "tests/unit/test_validators.py::TestSerialConfigValidator::test_invalid_baudrate", "tests/unit/test_validators.py::TestSerialConfigValidator::test_invalid_parity", "tests/unit/test_validators.py::TestSerialConfigValidator::test_missing_required_fields", "tests/unit/test_validators.py::TestSerialConfigValidator::test_valid_serial_config", "tests/unit/test_validators.py::TestSystemConfigValidator::test_invalid_buffer_size", "tests/unit/test_validators.py::TestSystemConfigValidator::test_invalid_log_level", "tests/unit/test_validators.py::TestSystemConfigValidator::test_invalid_threshold", "tests/unit/test_validators.py::TestSystemConfigValidator::test_valid_system_config"]