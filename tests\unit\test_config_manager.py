#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器单元测试

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import unittest
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.config_manager import (
    ConfigManager, SerialConfig, FrameDetectionConfig, DataFieldConfig,
    ResponseValidationConfig, CommandConfig, WorkModeConfig, ProtocolConfig,
    get_config_manager
)
from utils.exceptions import ConfigFileNotFoundError, ConfigParsingError


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        # 创建新的配置管理器实例用于测试
        self.config_manager = ConfigManager()
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = ConfigManager()
        manager2 = ConfigManager()
        self.assertIs(manager1, manager2)
        
        # 测试便捷函数
        manager3 = get_config_manager()
        self.assertIs(manager1, manager3)
    
    def test_load_system_config_default(self):
        """测试加载默认系统配置"""
        config = self.config_manager.load_system_config()
        
        self.assertIsInstance(config, dict)
        self.assertIn("system", config)
        self.assertIn("serial", config)
        self.assertIn("buffer", config)
        self.assertIn("queue", config)
        self.assertIn("error_handling", config)
        self.assertIn("logging", config)
    
    def test_load_system_config_from_file(self):
        """测试从文件加载系统配置"""
        # 创建临时配置文件
        test_config = {
            "buffer": {
                "size": 8192,
                "warning_threshold": 0.9
            },
            "queue": {
                "size": 200
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f)
            temp_file = f.name
        
        try:
            config = self.config_manager.load_system_config(temp_file)
            
            # 验证配置被正确合并
            self.assertEqual(config["buffer"]["size"], 8192)
            self.assertEqual(config["buffer"]["warning_threshold"], 0.9)
            self.assertEqual(config["queue"]["size"], 200)
            
            # 验证默认配置仍然存在
            self.assertIn("system", config)
            
        finally:
            Path(temp_file).unlink()
    
    def test_load_system_config_file_not_found(self):
        """测试加载不存在的系统配置文件"""
        with self.assertRaises(ConfigFileNotFoundError):
            self.config_manager.load_system_config("nonexistent.json")
    
    def test_load_system_config_invalid_json(self):
        """测试加载无效JSON配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("{ invalid json }")
            temp_file = f.name
        
        try:
            with self.assertRaises(ConfigParsingError):
                self.config_manager.load_system_config(temp_file)
        finally:
            Path(temp_file).unlink()
    
    def test_load_protocol_config(self):
        """测试加载协议配置"""
        # 创建临时协议配置文件
        protocol_config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "serial": {
                "port": "COM1",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 1.0
            },
            "frame_detection": {
                "header": "AA BB",
                "min_length": 8,
                "max_length": 256
            },
            "data_fields": [
                {
                    "name": "temperature",
                    "type": "int16",
                    "offset": 4,
                    "byte_order": "little",
                    "scale": 0.1,
                    "unit": "°C"
                }
            ],
            "commands": {
                "READ_DATA": {
                    "data": "01 03 00 00",
                    "description": "读取数据",
                    "response_validation": {
                        "strategy": "exact_match",
                        "expected_response": "01 03 02",
                        "timeout": 2.0
                    }
                }
            },
            "work_mode": {
                "mode": "continuous_command",
                "interval": 1.0,
                "commands": ["READ_DATA"]
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(protocol_config, f)
            temp_file = f.name
        
        try:
            config = self.config_manager.load_protocol_config(temp_file)
            
            # 验证协议配置对象
            self.assertIsInstance(config, ProtocolConfig)
            self.assertEqual(config.name, "测试协议")
            self.assertEqual(config.version, "1.0.0")
            
            # 验证串口配置
            self.assertIsInstance(config.serial, SerialConfig)
            self.assertEqual(config.serial.port, "COM1")
            self.assertEqual(config.serial.baudrate, 9600)
            
            # 验证帧检测配置
            self.assertIsInstance(config.frame_detection, FrameDetectionConfig)
            self.assertEqual(config.frame_detection.header, "AA BB")
            
            # 验证数据字段配置
            self.assertEqual(len(config.data_fields), 1)
            field = config.data_fields[0]
            self.assertIsInstance(field, DataFieldConfig)
            self.assertEqual(field.name, "temperature")
            self.assertEqual(field.type, "int16")
            
            # 验证指令配置
            self.assertIn("READ_DATA", config.commands)
            cmd = config.commands["READ_DATA"]
            self.assertIsInstance(cmd, CommandConfig)
            self.assertEqual(cmd.data, "01 03 00 00")
            
            # 验证应答验证配置
            self.assertIsInstance(cmd.response_validation, ResponseValidationConfig)
            self.assertEqual(cmd.response_validation.strategy, "exact_match")
            
            # 验证工作模式配置
            self.assertIsInstance(config.work_mode, WorkModeConfig)
            self.assertEqual(config.work_mode.mode, "continuous_command")
            
        finally:
            Path(temp_file).unlink()
    
    def test_get_config_methods(self):
        """测试获取配置的方法"""
        # 测试未加载协议配置时
        self.assertIsNone(self.config_manager.get_protocol_config())
        self.assertIsNone(self.config_manager.get_serial_config())
        self.assertIsNone(self.config_manager.get_frame_detection_config())
        self.assertEqual(self.config_manager.get_data_fields_config(), [])
        self.assertEqual(self.config_manager.get_commands_config(), {})
        self.assertIsNone(self.config_manager.get_work_mode_config())
        self.assertFalse(self.config_manager.is_protocol_loaded())
        
        # 加载协议配置后测试
        protocol_config = {
            "name": "测试协议",
            "version": "1.0.0",
            "description": "测试协议描述",
            "serial": {
                "port": "COM1",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 1.0
            },
            "frame_detection": {
                "header": "AA BB"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(protocol_config, f)
            temp_file = f.name
        
        try:
            self.config_manager.load_protocol_config(temp_file)
            
            # 测试加载后的方法
            self.assertIsNotNone(self.config_manager.get_protocol_config())
            self.assertIsNotNone(self.config_manager.get_serial_config())
            self.assertIsNotNone(self.config_manager.get_frame_detection_config())
            self.assertTrue(self.config_manager.is_protocol_loaded())
            
            # 测试配置文件路径
            config_path = self.config_manager.get_config_file_path()
            self.assertIsNotNone(config_path)
            self.assertEqual(str(config_path), temp_file)
            
        finally:
            Path(temp_file).unlink()
    
    def test_list_available_protocols(self):
        """测试列出可用协议"""
        # 创建临时协议目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一些协议文件
            protocol_files = ["protocol1.json", "protocol2.json", "not_json.txt"]
            for filename in protocol_files:
                (Path(temp_dir) / filename).touch()
            
            protocols = self.config_manager.list_available_protocols(temp_dir)
            
            # 应该只返回JSON文件
            self.assertEqual(len(protocols), 2)
            self.assertIn("protocol1.json", protocols)
            self.assertIn("protocol2.json", protocols)
            self.assertNotIn("not_json.txt", protocols)
    
    def test_create_default_protocol_template(self):
        """测试创建默认协议模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "template.json"
            
            self.config_manager.create_default_protocol_template(str(template_path))
            
            # 验证文件已创建
            self.assertTrue(template_path.exists())
            
            # 验证文件内容
            with open(template_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            self.assertIn("name", template_data)
            self.assertIn("version", template_data)
            self.assertIn("serial", template_data)
            self.assertIn("frame_detection", template_data)
            self.assertIn("data_fields", template_data)
            self.assertIn("commands", template_data)
            self.assertIn("work_mode", template_data)


class TestDataClasses(unittest.TestCase):
    """数据类测试"""
    
    def test_serial_config(self):
        """测试串口配置数据类"""
        config = SerialConfig(
            port="COM1",
            baudrate=115200,
            timeout=2.0
        )
        
        self.assertEqual(config.port, "COM1")
        self.assertEqual(config.baudrate, 115200)
        self.assertEqual(config.timeout, 2.0)
        # 测试默认值
        self.assertEqual(config.bytesize, 8)
        self.assertEqual(config.parity, 'N')
    
    def test_frame_detection_config(self):
        """测试帧检测配置数据类"""
        config = FrameDetectionConfig(
            header="AA BB",
            footer="CC DD",
            min_length=10
        )
        
        self.assertEqual(config.header, "AA BB")
        self.assertEqual(config.footer, "CC DD")
        self.assertEqual(config.min_length, 10)
        # 测试默认值
        self.assertEqual(config.max_length, 1024)
    
    def test_data_field_config(self):
        """测试数据字段配置数据类"""
        config = DataFieldConfig(
            name="temperature",
            type="int16",
            offset=4,
            scale=0.1,
            unit="°C"
        )
        
        self.assertEqual(config.name, "temperature")
        self.assertEqual(config.type, "int16")
        self.assertEqual(config.offset, 4)
        self.assertEqual(config.scale, 0.1)
        self.assertEqual(config.unit, "°C")
        # 测试默认值
        self.assertEqual(config.byte_order, "little")
        self.assertEqual(config.offset_value, 0.0)


if __name__ == '__main__':
    unittest.main()
