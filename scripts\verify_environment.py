#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境验证脚本 - 数据采集系统
验证Conda环境是否正确安装和配置

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import sys
import os
import subprocess
import importlib
import platform
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 颜色输出支持
class Colors:
    """控制台颜色输出类"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_header(title: str) -> None:
    """打印标题头部"""
    print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{title.center(60)}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}\n")

def print_success(message: str) -> None:
    """打印成功信息"""
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")

def print_error(message: str) -> None:
    """打印错误信息"""
    print(f"{Colors.RED}❌ {message}{Colors.END}")

def print_warning(message: str) -> None:
    """打印警告信息"""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")

def print_info(message: str) -> None:
    """打印信息"""
    print(f"{Colors.CYAN}ℹ️  {message}{Colors.END}")

def check_python_version() -> bool:
    """检查Python版本"""
    print_info("检查Python版本...")
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    
    print(f"   当前Python版本: {version_str}")
    print(f"   Python路径: {sys.executable}")
    
    # 检查是否为推荐版本 (3.8+)
    if version.major == 3 and version.minor >= 8:
        print_success(f"Python版本 {version_str} 符合要求 (>= 3.8)")
        return True
    else:
        print_error(f"Python版本 {version_str} 不符合要求，建议使用Python 3.8+")
        return False

def check_conda_installation() -> bool:
    """检查Conda是否正确安装"""
    print_info("检查Conda安装状态...")
    
    try:
        # 检查conda命令是否可用
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            conda_version = result.stdout.strip()
            print(f"   {conda_version}")
            print_success("Conda已正确安装")
            
            # 检查conda环境信息
            env_result = subprocess.run(['conda', 'info', '--envs'], 
                                      capture_output=True, text=True, timeout=10)
            if env_result.returncode == 0:
                print(f"   可用环境列表:")
                for line in env_result.stdout.split('\n')[2:]:  # 跳过头部信息
                    if line.strip() and not line.startswith('#'):
                        print(f"     {line.strip()}")
            
            return True
        else:
            print_error("Conda命令执行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("Conda命令执行超时")
        return False
    except FileNotFoundError:
        print_error("未找到conda命令，请确认Conda已正确安装并添加到PATH")
        return False
    except Exception as e:
        print_error(f"检查Conda时发生错误: {str(e)}")
        return False

def check_required_packages() -> Dict[str, bool]:
    """检查必需的Python包"""
    print_info("检查必需的Python包...")
    
    # 项目必需的核心包
    required_packages = {
        'pyserial': '>=3.5',
        'jsonschema': '>=3.2.0',
        'dataclasses': 'built-in',  # Python 3.7+内置
        'threading': 'built-in',
        'queue': 'built-in',
        'json': 'built-in',
        'pathlib': 'built-in',
        'typing': 'built-in'
    }
    
    # 可选的开发和测试包
    optional_packages = {
        'pytest': '>=6.0',
        'pytest-cov': '>=2.10',
        'black': '>=21.0',
        'flake8': '>=3.8',
        'mypy': '>=0.800'
    }
    
    results = {}
    
    print("   核心依赖包:")
    for package, version_req in required_packages.items():
        try:
            if version_req == 'built-in':
                # 内置模块检查
                importlib.import_module(package)
                print_success(f"     {package} (内置模块)")
                results[package] = True
            else:
                # 第三方包检查
                module = importlib.import_module(package)
                if hasattr(module, '__version__'):
                    version = module.__version__
                    print_success(f"     {package} {version}")
                else:
                    print_success(f"     {package} (已安装)")
                results[package] = True
                
        except ImportError:
            print_error(f"     {package} {version_req} - 未安装")
            results[package] = False
    
    print("\n   可选开发包:")
    for package, version_req in optional_packages.items():
        try:
            module = importlib.import_module(package)
            if hasattr(module, '__version__'):
                version = module.__version__
                print_success(f"     {package} {version}")
            else:
                print_success(f"     {package} (已安装)")
            results[package] = True
        except ImportError:
            print_warning(f"     {package} {version_req} - 未安装 (可选)")
            results[package] = False
    
    return results

def check_project_structure() -> bool:
    """检查项目目录结构"""
    print_info("检查项目目录结构...")
    
    project_root = Path.cwd()
    print(f"   项目根目录: {project_root}")
    
    # 检查是否在正确的项目目录
    if project_root.name != "DataStudio":
        print_warning(f"当前目录 '{project_root.name}' 可能不是预期的项目根目录 'DataStudio'")
    
    # 检查关键目录和文件
    expected_items = [
        ('.serena', 'dir', '项目配置目录'),
        ('.serena/memories', 'dir', 'Serena记忆文档目录'),
        ('scripts', 'dir', '脚本目录'),
    ]
    
    all_exist = True
    for item_path, item_type, description in expected_items:
        full_path = project_root / item_path
        if item_type == 'dir':
            if full_path.is_dir():
                print_success(f"     {item_path}/ - {description}")
            else:
                print_warning(f"     {item_path}/ - {description} (不存在)")
                all_exist = False
        else:  # file
            if full_path.is_file():
                print_success(f"     {item_path} - {description}")
            else:
                print_warning(f"     {item_path} - {description} (不存在)")
                all_exist = False
    
    return all_exist

def check_system_info() -> None:
    """显示系统信息"""
    print_info("系统环境信息:")
    
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   处理器架构: {platform.machine()}")
    print(f"   Python实现: {platform.python_implementation()}")
    print(f"   Python编译器: {platform.python_compiler()}")

def generate_environment_yml() -> None:
    """生成environment.yml文件"""
    print_info("生成Conda环境配置文件...")
    
    environment_content = """name: datastudio
channels:
  - conda-forge
  - defaults
dependencies:
  - python>=3.8
  - pip
  - pip:
    - pyserial>=3.5
    - jsonschema>=3.2.0
    - pytest>=6.0
    - pytest-cov>=2.10
    - black>=21.0
    - flake8>=3.8
    - mypy>=0.800
"""
    
    env_file = Path.cwd() / 'environment.yml'
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(environment_content)
        print_success(f"已生成环境配置文件: {env_file}")
        
        print_info("使用以下命令创建和激活环境:")
        print(f"   {Colors.YELLOW}conda env create -f environment.yml{Colors.END}")
        print(f"   {Colors.YELLOW}conda activate datastudio{Colors.END}")
        
    except Exception as e:
        print_error(f"生成环境配置文件失败: {str(e)}")

def main() -> None:
    """主函数"""
    print_header("数据采集系统 - 环境验证工具")
    
    # 系统信息
    check_system_info()
    
    # 检查项目结构
    #structure_ok = check_project_structure()
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查Conda安装
    conda_ok = check_conda_installation()
    
    # 检查必需包
    packages_result = check_required_packages()
    
    # 统计结果
    print_header("验证结果摘要")
    
    core_packages = ['pyserial', 'jsonschema', 'dataclasses', 'threading', 'queue', 'json', 'pathlib', 'typing']
    core_packages_ok = all(packages_result.get(pkg, False) for pkg in core_packages)
    
    if python_ok:
        print_success("Python版本检查通过")
    else:
        print_error("Python版本检查失败")
    
    if conda_ok:
        print_success("Conda安装检查通过")
    else:
        print_error("Conda安装检查失败")
    
    if core_packages_ok:
        print_success("核心依赖包检查通过")
    else:
        print_error("核心依赖包检查失败")
        missing_packages = [pkg for pkg in core_packages if not packages_result.get(pkg, False)]
        print_info(f"缺失的包: {', '.join(missing_packages)}")
    
    # 总体评估
    overall_ok = python_ok and conda_ok and core_packages_ok
    
    print(f"\n{Colors.BOLD}{'='*60}{Colors.END}")
    if overall_ok:
        print_success("🎉 环境验证通过！可以开始项目开发")
    else:
        print_error("❌ 环境验证失败，请解决上述问题后重新验证")
        
        # 生成环境配置文件
        if conda_ok:
            generate_environment_yml()
    
    print(f"{Colors.BOLD}{'='*60}{Colors.END}\n")

if __name__ == "__main__":
    main()
