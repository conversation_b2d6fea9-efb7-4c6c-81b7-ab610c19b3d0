#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证器
提供JSON配置文件的验证功能

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import re
from typing import Dict, Any, List, Optional, Union
from jsonschema import validate, ValidationError as JsonSchemaValidationError

from .constants import (
    SerialDefaults, BufferDefaults, QueueDefaults, ErrorDefaults,
    DataTypes, ByteOrder, WorkMode, ValidationStrategy, ConfigDefaults
)
from .exceptions import ConfigValidationError
from .helper_utils import ValidationUtils


class ConfigValidator:
    """配置验证器基类"""
    
    def __init__(self):
        self.errors: List[str] = []
    
    def clear_errors(self) -> None:
        """清除错误列表"""
        self.errors.clear()
    
    def add_error(self, error_message: str) -> None:
        """添加错误信息"""
        self.errors.append(error_message)
    
    def has_errors(self) -> bool:
        """检查是否有错误"""
        return len(self.errors) > 0
    
    def get_errors(self) -> List[str]:
        """获取错误列表"""
        return self.errors.copy()
    
    def raise_if_errors(self, field_path: str = "") -> None:
        """如果有错误则抛出异常"""
        if self.has_errors():
            raise ConfigValidationError(
                f"配置验证失败: {'; '.join(self.errors)}",
                field_path=field_path,
                validation_errors=self.errors
            )


class SerialConfigValidator(ConfigValidator):
    """串口配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> bool:
        """
        验证串口配置
        
        Args:
            config: 串口配置字典
            
        Returns:
            验证是否通过
        """
        self.clear_errors()
        
        # 验证必需字段
        required_fields = ['port', 'baudrate', 'bytesize', 'parity', 'stopbits', 'timeout']
        for field in required_fields:
            if field not in config:
                self.add_error(f"缺少必需字段: {field}")
        
        if self.has_errors():
            return False
        
        # 验证端口
        port = config.get('port')
        if not isinstance(port, str) or not port.strip():
            self.add_error("端口名称必须是非空字符串")
        
        # 验证波特率
        baudrate = config.get('baudrate')
        if not ValidationUtils.is_valid_baudrate(baudrate):
            self.add_error(f"无效的波特率: {baudrate}")
        
        # 验证数据位
        bytesize = config.get('bytesize')
        if bytesize not in SerialDefaults.SUPPORTED_BYTESIZES:
            self.add_error(f"无效的数据位: {bytesize}")
        
        # 验证校验位
        parity = config.get('parity')
        if parity not in SerialDefaults.SUPPORTED_PARITIES:
            self.add_error(f"无效的校验位: {parity}")
        
        # 验证停止位
        stopbits = config.get('stopbits')
        if stopbits not in SerialDefaults.SUPPORTED_STOPBITS:
            self.add_error(f"无效的停止位: {stopbits}")
        
        # 验证超时时间
        timeout = config.get('timeout')
        if not ValidationUtils.is_valid_timeout(timeout):
            self.add_error(f"无效的超时时间: {timeout}")
        
        return not self.has_errors()


class ProtocolConfigValidator(ConfigValidator):
    """协议配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> bool:
        """
        验证协议配置
        
        Args:
            config: 协议配置字典
            
        Returns:
            验证是否通过
        """
        self.clear_errors()
        
        # 验证基本信息
        self._validate_basic_info(config)
        
        # 验证帧检测配置
        if 'frame_detection' in config:
            self._validate_frame_detection(config['frame_detection'])
        
        # 验证数据字段配置
        if 'data_fields' in config:
            self._validate_data_fields(config['data_fields'])
        
        # 验证指令配置
        if 'commands' in config:
            self._validate_commands(config['commands'])
        
        # 验证工作模式配置
        if 'work_mode' in config:
            self._validate_work_mode(config['work_mode'])
        
        return not self.has_errors()
    
    def _validate_basic_info(self, config: Dict[str, Any]) -> None:
        """验证基本信息"""
        required_fields = ['name', 'version', 'description']
        for field in required_fields:
            if field not in config:
                self.add_error(f"缺少必需字段: {field}")
            elif not isinstance(config[field], str) or not config[field].strip():
                self.add_error(f"字段 {field} 必须是非空字符串")
    
    def _validate_frame_detection(self, frame_config: Dict[str, Any]) -> None:
        """验证帧检测配置"""
        # 验证帧头
        if 'header' in frame_config:
            header = frame_config['header']
            if not isinstance(header, str) or not header.strip():
                self.add_error("帧头必须是非空字符串")
        
        # 验证帧尾
        if 'footer' in frame_config:
            footer = frame_config['footer']
            if not isinstance(footer, str) or not footer.strip():
                self.add_error("帧尾必须是非空字符串")
        
        # 验证长度配置
        if 'length' in frame_config:
            length_config = frame_config['length']
            if 'min_length' in length_config:
                min_len = length_config['min_length']
                if not isinstance(min_len, int) or min_len < 1:
                    self.add_error("最小长度必须是正整数")
            
            if 'max_length' in length_config:
                max_len = length_config['max_length']
                if not isinstance(max_len, int) or max_len < 1:
                    self.add_error("最大长度必须是正整数")
                
                # 检查最小长度和最大长度的关系
                if ('min_length' in length_config and 
                    length_config['min_length'] > max_len):
                    self.add_error("最小长度不能大于最大长度")
    
    def _validate_data_fields(self, fields: List[Dict[str, Any]]) -> None:
        """验证数据字段配置"""
        if not isinstance(fields, list):
            self.add_error("数据字段配置必须是列表")
            return
        
        field_names = set()
        for i, field in enumerate(fields):
            if not isinstance(field, dict):
                self.add_error(f"数据字段 {i} 必须是字典")
                continue
            
            # 验证字段名称
            if 'name' not in field:
                self.add_error(f"数据字段 {i} 缺少名称")
            else:
                name = field['name']
                if not isinstance(name, str) or not name.strip():
                    self.add_error(f"数据字段 {i} 名称必须是非空字符串")
                elif name in field_names:
                    self.add_error(f"数据字段名称重复: {name}")
                else:
                    field_names.add(name)
            
            # 验证数据类型
            if 'type' not in field:
                self.add_error(f"数据字段 {i} 缺少类型")
            else:
                data_type = field['type']
                valid_types = [dt.value for dt in DataTypes]
                if data_type not in valid_types:
                    self.add_error(f"数据字段 {i} 类型无效: {data_type}")
            
            # 验证偏移量
            if 'offset' in field:
                offset = field['offset']
                if not isinstance(offset, int) or offset < 0:
                    self.add_error(f"数据字段 {i} 偏移量必须是非负整数")
            
            # 验证字节序
            if 'byte_order' in field:
                byte_order = field['byte_order']
                valid_orders = [bo.value for bo in ByteOrder]
                if byte_order not in valid_orders:
                    self.add_error(f"数据字段 {i} 字节序无效: {byte_order}")
    
    def _validate_commands(self, commands: Dict[str, Any]) -> None:
        """验证指令配置"""
        if not isinstance(commands, dict):
            self.add_error("指令配置必须是字典")
            return
        
        for cmd_name, cmd_config in commands.items():
            if not isinstance(cmd_config, dict):
                self.add_error(f"指令 {cmd_name} 配置必须是字典")
                continue
            
            # 验证指令数据
            if 'data' not in cmd_config:
                self.add_error(f"指令 {cmd_name} 缺少数据")
            else:
                data = cmd_config['data']
                if not isinstance(data, str) or not data.strip():
                    self.add_error(f"指令 {cmd_name} 数据必须是非空字符串")
            
            # 验证应答验证配置
            if 'response_validation' in cmd_config:
                self._validate_response_validation(
                    cmd_config['response_validation'], 
                    f"指令 {cmd_name}"
                )
    
    def _validate_response_validation(self, validation_config: Dict[str, Any], 
                                    context: str) -> None:
        """验证应答验证配置"""
        if 'strategy' not in validation_config:
            self.add_error(f"{context} 应答验证缺少策略")
            return
        
        strategy = validation_config['strategy']
        valid_strategies = [vs.value for vs in ValidationStrategy]
        if strategy not in valid_strategies:
            self.add_error(f"{context} 应答验证策略无效: {strategy}")
        
        # 根据策略验证相应的配置
        if strategy == ValidationStrategy.EXACT_MATCH.value:
            if 'expected_response' not in validation_config:
                self.add_error(f"{context} 精确匹配策略缺少期望应答")
        
        elif strategy == ValidationStrategy.REGEX_MATCH.value:
            if 'pattern' not in validation_config:
                self.add_error(f"{context} 正则匹配策略缺少模式")
            else:
                pattern = validation_config['pattern']
                try:
                    re.compile(pattern)
                except re.error as e:
                    self.add_error(f"{context} 正则表达式无效: {e}")
    
    def _validate_work_mode(self, work_mode_config: Dict[str, Any]) -> None:
        """验证工作模式配置"""
        if 'mode' not in work_mode_config:
            self.add_error("工作模式配置缺少模式")
            return
        
        mode = work_mode_config['mode']
        valid_modes = [wm.value for wm in WorkMode]
        if mode not in valid_modes:
            self.add_error(f"工作模式无效: {mode}")
        
        # 验证循环查询模式的特定配置
        if mode == WorkMode.CONTINUOUS_COMMAND.value:
            if 'interval' in work_mode_config:
                interval = work_mode_config['interval']
                if not isinstance(interval, (int, float)) or interval <= 0:
                    self.add_error("循环查询间隔必须是正数")


class SystemConfigValidator(ConfigValidator):
    """系统配置验证器"""
    
    def validate(self, config: Dict[str, Any]) -> bool:
        """
        验证系统配置
        
        Args:
            config: 系统配置字典
            
        Returns:
            验证是否通过
        """
        self.clear_errors()
        
        # 验证缓冲区配置
        if 'buffer' in config:
            self._validate_buffer_config(config['buffer'])
        
        # 验证队列配置
        if 'queue' in config:
            self._validate_queue_config(config['queue'])
        
        # 验证错误处理配置
        if 'error_handling' in config:
            self._validate_error_handling_config(config['error_handling'])
        
        # 验证日志配置
        if 'logging' in config:
            self._validate_logging_config(config['logging'])
        
        return not self.has_errors()
    
    def _validate_buffer_config(self, buffer_config: Dict[str, Any]) -> None:
        """验证缓冲区配置"""
        if 'size' in buffer_config:
            size = buffer_config['size']
            if not isinstance(size, int) or size < BufferDefaults.MIN_FRAME_SIZE:
                self.add_error(f"缓冲区大小必须是不小于 {BufferDefaults.MIN_FRAME_SIZE} 的整数")
        
        if 'warning_threshold' in buffer_config:
            threshold = buffer_config['warning_threshold']
            if not isinstance(threshold, (int, float)) or not (0 < threshold < 1):
                self.add_error("缓冲区警告阈值必须是0到1之间的数值")
    
    def _validate_queue_config(self, queue_config: Dict[str, Any]) -> None:
        """验证队列配置"""
        if 'size' in queue_config:
            size = queue_config['size']
            if not isinstance(size, int) or size < 1:
                self.add_error("队列大小必须是正整数")
        
        if 'warning_threshold' in queue_config:
            threshold = queue_config['warning_threshold']
            if not isinstance(threshold, (int, float)) or not (0 < threshold < 1):
                self.add_error("队列警告阈值必须是0到1之间的数值")
        
        if 'batch_size' in queue_config:
            batch_size = queue_config['batch_size']
            if not isinstance(batch_size, int) or batch_size < 1:
                self.add_error("批处理大小必须是正整数")
    
    def _validate_error_handling_config(self, error_config: Dict[str, Any]) -> None:
        """验证错误处理配置"""
        if 'max_consecutive_errors' in error_config:
            max_errors = error_config['max_consecutive_errors']
            if not isinstance(max_errors, int) or max_errors < 1:
                self.add_error("最大连续错误次数必须是正整数")
        
        if 'retry_delay' in error_config:
            delay = error_config['retry_delay']
            if not isinstance(delay, (int, float)) or delay < 0:
                self.add_error("重试延迟必须是非负数")
        
        if 'max_retry_attempts' in error_config:
            attempts = error_config['max_retry_attempts']
            if not isinstance(attempts, int) or attempts < 0:
                self.add_error("最大重试次数必须是非负整数")
    
    def _validate_logging_config(self, logging_config: Dict[str, Any]) -> None:
        """验证日志配置"""
        if 'level' in logging_config:
            level = logging_config['level']
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if level not in valid_levels:
                self.add_error(f"日志级别无效: {level}")
        
        if 'max_file_size' in logging_config:
            size = logging_config['max_file_size']
            if not isinstance(size, int) or size < 1024:  # 至少1KB
                self.add_error("日志文件最大大小必须是不小于1024的整数")
        
        if 'max_files' in logging_config:
            files = logging_config['max_files']
            if not isinstance(files, int) or files < 1:
                self.add_error("日志文件最大数量必须是正整数")


# 便捷函数
def validate_serial_config(config: Dict[str, Any]) -> None:
    """
    验证串口配置的便捷函数
    
    Args:
        config: 串口配置字典
        
    Raises:
        ConfigValidationError: 验证失败时抛出
    """
    validator = SerialConfigValidator()
    validator.validate(config)
    validator.raise_if_errors("serial")


def validate_protocol_config(config: Dict[str, Any]) -> None:
    """
    验证协议配置的便捷函数
    
    Args:
        config: 协议配置字典
        
    Raises:
        ConfigValidationError: 验证失败时抛出
    """
    validator = ProtocolConfigValidator()
    validator.validate(config)
    validator.raise_if_errors("protocol")


def validate_system_config(config: Dict[str, Any]) -> None:
    """
    验证系统配置的便捷函数
    
    Args:
        config: 系统配置字典
        
    Raises:
        ConfigValidationError: 验证失败时抛出
    """
    validator = SystemConfigValidator()
    validator.validate(config)
    validator.raise_if_errors("system")
